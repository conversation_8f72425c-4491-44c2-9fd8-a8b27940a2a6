'use client'
import { cn } from '@ui/lib'
import Image from 'next/image'
import logo_img from '../../../public/images/logo.png'
import { useState, useCallback, useEffect } from 'react'
import { Link, useRouter } from '@i18n/routing' // Import router for programmatic navigation
//
// 首先定义一个类型接口
interface MenuItem {
  title: string
  desc: string
  href: string
  seoHref?: string
  isHot?: boolean
  isNew?: boolean
  coming?: boolean
  preview?: string
  hidden?: boolean
  point?: number
  icon?: JSX.Element
  isVideo?: boolean
}

// 获取工具对应的图标
const getToolIcon = (category: string, href?: string): JSX.Element => {
  // 如果提供了具体工具的href，根据路径返回对应图标
  if (href) {
    const toolIconMap: { [key: string]: JSX.Element } = {
      // 商业相关
      '/ai-clothes-changer': <ImageIcon className="w-5 h-5 text-purple-400" />,
      '/logo-generator': <ImageIcon className="w-5 h-5 text-purple-400" />,
      '/custom-model': <Settings className="w-5 h-5 text-purple-400" />,
      '/product-video': <Camera className="w-5 h-5 text-purple-400" />,

      // 创意相关
      '/ai-art-generator-free': <Zap className="w-5 h-5 text-fuchsia-400" />,
      '/sketch': <Zap className="w-5 h-5 text-fuchsia-400" />,
      '/ghibli': <Zap className="w-5 h-5 text-fuchsia-400" />,
      '/photo-to-anime': <Zap className="w-5 h-5 text-fuchsia-400" />,

      // 记忆修复
      '/photo-restore': <FileText className="w-5 h-5 text-pink-400" />,
      '/colorize': <FileText className="w-5 h-5 text-pink-400" />,
      '/color-enhance': <FileText className="w-5 h-5 text-pink-400" />,
      '/old-filter': <FileText className="w-5 h-5 text-pink-400" />,
      '/memory-video': <FileText className="w-5 h-5 text-pink-400" />,

      // 图像工具
      '/upscale': <Camera className="w-5 h-5 text-violet-400" />,
      '/ai-face-swap-video': <Camera className="w-5 h-5 text-violet-400" />,
      '/ai-face-swap': <Camera className="w-5 h-5 text-violet-400" />,
      '/ai-hug': <Zap className="w-5 h-5 text-violet-400" />,
      '/image-to-image': <Camera className="w-5 h-5 text-violet-400" />,
      '/text-to-image': <ImageIcon className="w-5 h-5 text-violet-400" />,
      '/ai-image-background-remover': (
        <Settings className="w-5 h-5 text-violet-400" />
      ),
      '/ai-remove-watermark': <Settings className="w-5 h-5 text-violet-400" />,
      '/element-extraction': <Settings className="w-5 h-5 text-violet-400" />,
      '/background-replacement': (
        <Settings className="w-5 h-5 text-violet-400" />
      ),
      '/enhance': <Camera className="w-5 h-5 text-violet-400" />,

      // 视频创作
      '/photo-to-video': <Camera className="w-5 h-5 text-purple-300" />,
      '/person-animation': <Camera className="w-5 h-5 text-purple-300" />,
      '/scene-video': <Camera className="w-5 h-5 text-purple-300" />,

      // 娱乐功能
      '/ai-portrait': <Zap className="w-5 h-5 text-pink-400" />,
      '/meme-generator': <Zap className="w-5 h-5 text-pink-400" />,

      // 工具类
      '/batch-process': <Settings className="w-5 h-5 text-indigo-400" />,
    }

    // 从完整路径中提取工具路径
    const toolPath = href.split('/').slice(-1)[0] // 获取最后一部分
    const fullPath = `/${toolPath}`

    return toolIconMap[fullPath] || getToolIcon(category)
  }

  // 根据类别选择合适的图标
  const iconMap = {
    business: <ImageIcon className="w-5 h-5 text-purple-400" />,
    creative: <Zap className="w-5 h-5 text-fuchsia-400" />,
    memory: <FileText className="w-5 h-5 text-pink-400" />,
    imageTools: <Camera className="w-5 h-5 text-violet-400" />,
    videoCreation: <Camera className="w-5 h-5 text-purple-300" />,
    utilities: <Settings className="w-5 h-5 text-indigo-400" />,
    fun: <Zap className="w-5 h-5 text-pink-400" />,
  }

  return (
    iconMap[category as keyof typeof iconMap] || (
      <Camera className="w-5 h-5 text-purple-400" />
    )
  )
}

// 抽取图标区域为单独组件
const ItemIcon = ({
  item,
  activeCategory,
}: {
  item: MenuItem
  activeCategory: string
}) => (
  <div className="w-10 h-10 rounded-lg overflow-hidden bg-gradient-to-br from-gray-800 to-gray-700 flex items-center justify-center shadow-md shadow-purple-500/10 border border-purple-500/20">
    {item.preview ? (
      <img src={item.preview} alt="" className="w-full h-full object-cover" />
    ) : (
      getToolIcon(activeCategory)
    )}
  </div>
)

// 积分标签组件
const PointLabel = ({ point }: { point: number }) => (
  <span className="inline-flex items-center justify-center bg-gradient-to-r from-amber-500 to-orange-500 text-white px-2 py-0.5 text-xs font-bold rounded-full ml-1.5 shadow-sm">
    {point} 积分
  </span>
)

// 抽取标题和描述部分为单独组件
const ItemContent = ({ item }: { item: MenuItem }) => (
  <div className="flex-1 min-w-0">
    <div className="flex items-center flex-wrap gap-y-1">
      <h3 className="font-medium text-gray-200 group-hover:text-purple-200 mr-1 transition-colors">
        {item.title}
      </h3>
      {item.isHot && <HotLabel />}
      {item.isNew && <NewLabel />}
      {item.coming && <ComingLabel />}
      {/* {item.point && <PointLabel point={item.point} />} */}
    </div>
    <p className="text-sm text-gray-400 mt-1">{item.desc}</p>
  </div>
)

import {
  ChevronRight,
  Camera,
  Image as ImageIcon,
  FileText,
  Settings,
  Zap,
  ChevronDown,
  X,
} from 'lucide-react'

import { usePathname } from '@i18n/routing'
import { useTranslations } from 'next-intl'

// 热门和新品标签组件 - 调整为更适合深色背景的渐变
const HotLabel = () => (
  <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-fuchsia-500 to-purple-600 px-2 py-0.5 text-xs font-medium text-white ml-1.5 shadow-sm">
    Hot
  </span>
)

const NewLabel = () => (
  <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-violet-500 to-purple-600 px-2 py-0.5 text-xs font-medium text-white ml-1.5 shadow-sm">
    New
  </span>
)

// 正在来的路上标签组件
const ComingLabel = () => (
  <span className="inline-flex items-center justify-center bg-orange-800/60 text-orange-300 px-2 py-0.5 text-xs font-medium rounded ml-1.5 whitespace-nowrap border border-orange-500/20">
    Coming
  </span>
)

// 导出菜单数据生成函数
export const getMenuCategories = (isAiPage: boolean = false, t: any) => {
  const getHrefPrefix = () => {
    return isAiPage ? '/ai' : '/tools'
  }

  return {
    business: {
      label: t('tools.business.label'),
      bgClass: 'from-purple-900/40 to-purple-800/40',
      items: [
        // {
        //   title: '场景图生成',
        //   desc: '智能生成多场景商品展示图',
        //   href: `${getHrefPrefix()}/scene-generator`,
        //   seoHref: `${getHrefPrefix()}/scene-generator`,
        //   coming: true,
        // },
        {
          id: 'product-video',
          title: t('tools.business.items.productVideo.title'),
          desc: t('tools.business.items.productVideo.desc'),
          href: `${getHrefPrefix()}/product-video`,
          seoHref: `${getHrefPrefix()}/product-video`,
          coming: true,
          point: 10,
          icon: <Camera className="w-5 h-5 text-purple-400" />,
          isVideo: true,
          mediaUrl: '/videos/ai-image-to-video/product-animate.mp4',
        },
        {
          id: 'ai-clothes-changer',
          title: t('tools.business.items.aiClothesChanger.title'),
          desc: t('tools.business.items.aiClothesChanger.desc'),
          href: `${getHrefPrefix()}/ai-clothes-changer`,
          seoHref: `${getHrefPrefix()}/ai-clothes-changer`,
          isHot: true,
          point: 4,
          icon: <ImageIcon className="w-5 h-5 text-purple-400" />,
          mediaUrl: '/images/ai-clothes-changer/use-case-3.jpg',
        },
        // {
        //   title: '高转化率姿势',
        //   desc: '专业模特姿势库',
        //   href: `${getHrefPrefix()}/model-poses`,
        //   seoHref: `${getHrefPrefix()}/model-poses`,
        //   coming: true,
        // },
        {
          id: 'custom-model',
          title: t('tools.business.items.customModel.title'),
          desc: t('tools.business.items.customModel.desc'),
          href: `${getHrefPrefix()}/custom-model`,
          seoHref: `${getHrefPrefix()}/custom-model`,
          coming: true,
          point: 15,
          icon: <Settings className="w-5 h-5 text-purple-400" />,
          mediaUrl: '/images/color-enhance/photo-custom.png',
        },
        // 是要做的
        // {
        //   title: t('tools.business.items.logoGenerator.title'),
        //   desc: t('tools.business.items.logoGenerator.desc'),
        //   href: `${getHrefPrefix()}/logo-generator`,
        //   seoHref: `${getHrefPrefix()}/logo-generator`,
        //   point: 6,
        //   icon: <ImageIcon className="w-5 h-5 text-purple-400" />,
        // },
      ],
    },
    creative: {
      label: t('tools.creative.label'),
      bgClass: 'from-violet-900/40 to-fuchsia-800/40',
      items: [
        // {
        //   title: '营销海报',
        //   desc: '专业广告海报设计',
        //   href: `${getHrefPrefix()}/poster`,
        //   seoHref: `${getHrefPrefix()}/poster`,
        //   // isHot: true,
        //   coming: true,
        // },
        {
          id: 'ghibli',
          title: t('tools.creative.items.ghibli.title'),
          desc: t('tools.creative.items.ghibli.desc'),
          href: `${getHrefPrefix()}/ghibli`,
          seoHref: `${getHrefPrefix()}/ghibli`,
          isHot: true,
          point: 2,
          icon: <Zap className="w-5 h-5 text-fuchsia-400" />,
          mediaUrl: '/ghibli/ghibli-1.jpg',
        },
        {
          id: 'photo-to-anime',
          title: t('tools.creative.items.photoToAnime.title'),
          desc: t('tools.creative.items.photoToAnime.desc'),
          href: `${getHrefPrefix()}/photo-to-anime`,
          seoHref: `${getHrefPrefix()}/photo-to-anime`,
          point: 2,
          icon: <Zap className="w-5 h-5 text-fuchsia-400" />,
          mediaUrl: '/images/photo-to-anime/use-case-1.jpg',
        },
        {
          id: 'ai-art-generator-free',
          title: t('tools.creative.items.aiArtGenerator.title'),
          desc: t('tools.creative.items.aiArtGenerator.desc'),
          href: `${getHrefPrefix()}/ai-art-generator-free`,
          seoHref: `${getHrefPrefix()}/ai-art-generator-free`,
          point: 2,
          icon: <Zap className="w-5 h-5 text-fuchsia-400" />,
          mediaUrl:
            '/images/ai-art-generator-free/Snipaste_2025-06-29_15-52-03.png',
        },
        {
          id: 'sketch-to-image',
          title: t('tools.creative.items.sketch.title'),
          desc: t('tools.creative.items.sketch.desc'),
          href: `${getHrefPrefix()}/sketch`,
          seoHref: `${getHrefPrefix()}/sketch`,
          point: 2,
          icon: <Zap className="w-5 h-5 text-fuchsia-400" />,
          mediaUrl: '/samples/sketch-1.png',
        },
      ],
    },
    memory: {
      label: t('tools.memory.label'),
      bgClass: 'from-purple-900/40 to-pink-800/40',
      items: [
        {
          id: 'photo-restore',
          title: t('tools.memory.items.photoRestore.title'),
          desc: t('tools.memory.items.photoRestore.desc'),
          href: `${getHrefPrefix()}/photo-restore`,
          seoHref: `${getHrefPrefix()}/photo-restore`,
          isHot: true,
          point: 3,
          icon: <FileText className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/samples/enhancer restore old photo.gif',
        },
        {
          id: 'colorize',
          title: t('tools.memory.items.colorize.title'),
          desc: t('tools.memory.items.colorize.desc'),
          href: `${getHrefPrefix()}/colorize`,
          seoHref: `${getHrefPrefix()}/colorize`,
          point: 3,
          icon: <FileText className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/images/color-enhance/photo-colorize.png',
        },
        {
          id: 'color-enhance',
          title: t('tools.memory.items.colorEnhance.title'),
          desc: t('tools.memory.items.colorEnhance.desc'),
          href: `${getHrefPrefix()}/color-enhance`,
          seoHref: `${getHrefPrefix()}/color-enhance`,
          point: 2,
          icon: <FileText className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/images/color-enhance/before-after.png',
        },
        {
          id: 'old-filter',
          title: t('tools.memory.items.oldFilter.title'),
          desc: t('tools.memory.items.oldFilter.desc'),
          href: `${getHrefPrefix()}/old-filter`,
          seoHref: `${getHrefPrefix()}/old-filter`,
          isNew: true,
          point: 2,
          icon: <FileText className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/samples/age-case6.png',
        },
        {
          id: 'memory-video',
          title: t('tools.memory.items.memoryVideo.title'),
          desc: t('tools.memory.items.memoryVideo.desc'),
          href: `${getHrefPrefix()}/memory-video`,
          seoHref: `${getHrefPrefix()}/memory-video`,
          point: 4,
          icon: <FileText className="w-5 h-5 text-pink-400" />,
          isVideo: true,
          mediaUrl: '/samples/memorial-video-maker/A-4-after.mp4',
        },
        // {
        //   title: 'Memorial Video',
        //   desc: '专业追思视频制作',
        //   href: `${getHrefPrefix()}/memorial-video`,
        //   seoHref: `${getHrefPrefix()}/memorial-video`,
        //   coming: true,
        // },
      ],
    },
    imageTools: {
      label: t('tools.imageTools.label'),
      bgClass: 'from-fuchsia-900/40 to-indigo-800/40',
      items: [
        {
          id: 'image-to-image',
          title: t('tools.imageTools.items.imageToImage.title'),
          desc: t('tools.imageTools.items.imageToImage.desc'),
          href: `${getHrefPrefix()}/image-to-image`,
          seoHref: `${getHrefPrefix()}/image-to-image`,
          isHot: true,
          coming: true,
          point: 3,
          icon: <Camera className="w-5 h-5 text-violet-400" />,
          mediaUrl: '/images/ai-clothes-changer/use-case-2.jpg',
        },
        {
          id: 'text-to-image',
          title: t('tools.imageTools.items.textToImage.title'),
          desc: t('tools.imageTools.items.textToImage.desc'),
          href: `${getHrefPrefix()}/ai-text-to-image`,
          seoHref: `${getHrefPrefix()}/ai-text-to-image`,
          isHot: true,
          point: 3,
          icon: <ImageIcon className="w-5 h-5 text-violet-400" />,
          mediaUrl: '/images/home/<USER>',
        },
        {
          id: 'image-to-video',
          title: t('tools.imageTools.items.imageToVideo.title'),
          desc: t('tools.imageTools.items.imageToVideo.desc'),
          href: `${getHrefPrefix()}/photo-to-video`,
          seoHref: `${getHrefPrefix()}/photo-to-video`,
          isHot: true,
          // coming: true,
          point: 8,
          icon: <Camera className="w-5 h-5 text-violet-400" />,
          isVideo: true,
          mediaUrl:
            'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/B2.mp4',
        },
        // {
        //   title: 'AI修图大师',
        //   desc: '在线PS级修图工具',
        //   href: `${getHrefPrefix()}/ai-retouch`,
        //   seoHref: `${getHrefPrefix()}/ai-retouch`,
        //   coming: true,
        // },
        {
          id: 'background-removal',
          title: t('tools.imageTools.items.backgroundRemoval.title'),
          desc: t('tools.imageTools.items.backgroundRemoval.desc'),
          href: `${getHrefPrefix()}/ai-image-background-remover`,
          seoHref: `${getHrefPrefix()}/ai-image-background-remover`,
          // coming: true,
          point: 3,
          bgRemove: true,
          beforeImage: '/images/ai-image-background-remover/woman-before.png',
          afterImage: '/images/ai-image-background-remover/woman-after.png',
          icon: <Settings className="w-5 h-5 text-violet-400" />,
          mediaUrl: '/images/ai-clothes-changer/use-case-1.jpg',
        },
        // {
        //   title: '一键消除',
        //   desc: '智能移除不需要的元素',
        //   href: `${getHrefPrefix()}/object-removal`,
        //   seoHref: `${getHrefPrefix()}/object-removal`,
        //   coming: true,
        // },
        {
          id: 'element-extraction',
          title: t('tools.imageTools.items.elementExtraction.title'),
          desc: t('tools.imageTools.items.elementExtraction.desc'),
          href: `${getHrefPrefix()}/element-extraction`,
          seoHref: `${getHrefPrefix()}/element-extraction`,
          coming: true,
          point: 3,
          icon: <Settings className="w-5 h-5 text-violet-400" />,
          mediaUrl: '/images/templates/extract.png',
        },
        {
          id: 'upscale',
          title: t('tools.imageTools.items.upscale.title'),
          desc: t('tools.imageTools.items.upscale.desc'),
          href: `${getHrefPrefix()}/upscale`,
          seoHref: `${getHrefPrefix()}/upscale`,
          point: 3,
          bgRemove: true,
          beforeImage: '/samples/upscale_before.webp',
          afterImage: '/samples/upscale_after.webp',
          icon: <Camera className="w-5 h-5 text-violet-400" />,
          mediaUrl: '',
        },
        // {
        //   title: '智能扩图',
        //   desc: 'AI智能扩展图片画面',
        //   href: `${getHrefPrefix()}/outpainting`,
        //   seoHref: `${getHrefPrefix()}/outpainting`,
        //   isNew: true,
        // },
        {
          id: 'background-replacement',
          title: t('tools.imageTools.items.backgroundReplacement.title'),
          desc: t('tools.imageTools.items.backgroundReplacement.desc'),
          href: `${getHrefPrefix()}/background-replacement`,
          seoHref: `${getHrefPrefix()}/background-replacement`,
          coming: true,
          point: 4,
          bgRemove: true,
          beforeImage:
            '/images/ai-background-replace/stunning-quality-car-before.png',
          afterImage:
            '/images/ai-background-replace/stunning-quality-car-replace.png',
          icon: <Settings className="w-5 h-5 text-violet-400" />,
          mediaUrl: '',
        },
        {
          id: 'enhance',
          title: t('tools.imageTools.items.enhance.title'),
          desc: t('tools.imageTools.items.enhance.desc'),
          href: `${getHrefPrefix()}/enhance`,
          seoHref: `${getHrefPrefix()}/enhance`,
          coming: true,
          point: 3,
          icon: <Camera className="w-5 h-5 text-violet-400" />,
          mediaUrl: '/images/ai-headshot-generator/id-1.webp',
        },
        {
          id: 'ai-remove-watermark',
          title: t('tools.imageTools.items.aiRemoveWatermark.title'),
          desc: t('tools.imageTools.items.aiRemoveWatermark.desc'),
          href: `${getHrefPrefix()}/ai-remove-watermark`,
          seoHref: `${getHrefPrefix()}/ai-remove-watermark`,
          point: 3,
          icon: <Settings className="w-5 h-5 text-violet-400" />,
          mediaUrl: '/images/ai-remove-watermark/sample-before1.png',
        },
      ],
    },
    videoCreation: {
      label: t('tools.videoCreation.label'),
      bgClass: 'from-violet-900/40 to-purple-800/40',
      items: [
        {
          id: 'photo-to-video',
          title: t('tools.videoCreation.items.photoToVideo.title'),
          desc: t('tools.videoCreation.items.photoToVideo.desc'),
          href: `${getHrefPrefix()}/photo-to-video`,
          seoHref: `${getHrefPrefix()}/photo-to-video`,
          // isHot: true,
          coming: true,
          point: 8,
          icon: <Camera className="w-5 h-5 text-purple-300" />,
          isVideo: true,
          mediaUrl:
            'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/D2.mp4',
        },
        {
          id: 'ai-face-swap-video',
          title: t('tools.videoCreation.items.aiFaceSwapVideo.title'),
          desc: t('tools.videoCreation.items.aiFaceSwapVideo.desc'),
          href: `${getHrefPrefix()}/ai-face-swap-video`,
          seoHref: `${getHrefPrefix()}/ai-face-swap-video`,
          point: 12,
          icon: <Camera className="w-5 h-5 text-purple-300" />,
          isVideo: true,
          mediaUrl: '/images/templates/face-swap.png',
        },
        // {
        //   title: '商品展示视频',
        //   desc: '专业商品展示动画',
        //   href: `${getHrefPrefix()}/product-showcase`,
        //   seoHref: `${getHrefPrefix()}/product-showcase`,
        //   coming: true,
        // },
        {
          id: 'person-animation',
          title: t('tools.videoCreation.items.personAnimation.title'),
          desc: t('tools.videoCreation.items.personAnimation.desc'),
          href: `${getHrefPrefix()}/person-animation`,
          seoHref: `${getHrefPrefix()}/person-animation`,
          // isNew: true,
          coming: true,
          point: 8,
          icon: <Camera className="w-5 h-5 text-purple-300" />,
          isVideo: true,
          mediaUrl:
            'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/C2.mp4',
        },
        {
          id: 'scene-video',
          title: t('tools.videoCreation.items.sceneVideo.title'),
          desc: t('tools.videoCreation.items.sceneVideo.desc'),
          href: `${getHrefPrefix()}/scene-video`,
          seoHref: `${getHrefPrefix()}/scene-video`,
          coming: true,
          point: 14,
          icon: <Camera className="w-5 h-5 text-purple-300" />,
          isVideo: true,
          mediaUrl: '/videos/templates/scree_1_V2.mp4',
        },
        // {
        //   title: '视频风格转换',
        //   desc: '一键转换视频风格',
        //   href: `${getHrefPrefix()}/video-style`,
        //   seoHref: `${getHrefPrefix()}/video-style`,
        //   coming: true,
        // },
      ],
    },
    utilities: {
      label: t('tools.utilities.label'),
      bgClass: 'from-purple-900/40 to-fuchsia-800/40',
      items: [
        {
          id: 'batch-process',
          title: t('tools.utilities.items.batchProcess.title'),
          desc: t('tools.utilities.items.batchProcess.desc'),
          href: `${getHrefPrefix()}/batch-process`,
          seoHref: `${getHrefPrefix()}/batch-process`,
          // isHot: true,
          coming: true,
          point: 20,
          icon: <Settings className="w-5 h-5 text-indigo-400" />,
          mediaUrl: '/images/templates/bg-remove.webp',
        },
      ],
    },
    fun: {
      label: t('tools.fun.label'),
      bgClass: 'from-pink-900/40 to-purple-800/40',
      items: [
        {
          id: 'ai-portrait',
          title: t('tools.fun.items.aiPortrait.title'),
          desc: t('tools.fun.items.aiPortrait.desc'),
          href: `${getHrefPrefix()}/ai-portrait`,
          seoHref: `${getHrefPrefix()}/ai-portrait`,
          // isHot: true,
          coming: true,
          point: 8,
          icon: <Zap className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/images/templates/portrait.png',
        },
        {
          id: 'ai-face-swap',
          title: t('tools.fun.items.aiFaceSwap.title'),
          desc: t('tools.fun.items.aiFaceSwap.desc'),
          href: `${getHrefPrefix()}/ai-face-swap`,
          seoHref: `${getHrefPrefix()}/ai-face-swap`,
          isHot: true,
          point: 3,
          icon: <Zap className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/images/ai-face-swap/use-case-1.jpg',
        },
        {
          id: 'ai-hug',
          title: t('tools.fun.items.aiHug.title'),
          desc: t('tools.fun.items.aiHug.desc'),
          href: `${getHrefPrefix()}/ai-hug`,
          seoHref: `${getHrefPrefix()}/ai-hug`,
          isHot: true,
          point: 8,
          icon: <Zap className="w-5 h-5 text-pink-400" />,
          isVideo: true,
          mediaUrl:
            'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/A-2.mp4',
        },
        {
          id: 'ai-tattoo-generator',
          title: t('tools.fun.items.aiTattooGenerator.title'),
          desc: t('tools.fun.items.aiTattooGenerator.desc'),
          href: `${getHrefPrefix()}/ai-tattoo-generator`,
          seoHref: `${getHrefPrefix()}/ai-tattoo-generator`,
          isHot: true,
          point: 2,
          icon: <Zap className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/images/templates/tattoo.png',
        },
        {
          id: 'meme-generator',
          title: t('tools.fun.items.memeGenerator.title'),
          desc: t('tools.fun.items.memeGenerator.desc'),
          href: `${getHrefPrefix()}/meme-generator`,
          seoHref: `${getHrefPrefix()}/meme-generator`,
          coming: true,
          point: 2,
          icon: <Zap className="w-5 h-5 text-pink-400" />,
          mediaUrl: '/images/templates/meme.png',
        },
      ],
    },
  }
}

// 导出获取视频相关菜单项的函数
export const getVideoMenuItems = (isAiPage: boolean = false, t: any) => {
  // if (typeof window === 'undefined') return [] // SSR 安全性检查

  const categories = getMenuCategories(isAiPage, t)
  const videoItems: MenuItem[] = []

  // 遍历所有分类，收集 isVideo: true 的项目
  Object.values(categories).forEach((category: any) => {
    if (category.items) {
      category.items.forEach((item: MenuItem) => {
        if (item.isVideo) {
          videoItems.push(item)
        }
      })
    }
  })

  return videoItems
}

export function Logo({
  withLabel = false,
  className,
}: {
  className?: string
  withLabel?: boolean
}) {
  const t = useTranslations()
  const [isHovered, setIsHovered] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeCategory, setActiveCategory] = useState('business')
  const [previewItem, setPreviewItem] = useState(null)
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()
  const router = useRouter() // Initialize router

  // 检查是否在 AI 页面
  const isAiPage = pathname.startsWith('/ai/')

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024) // lg breakpoint
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 根据页面类型获取正确的 href 前缀
  const getHrefPrefix = () => (isAiPage ? '/ai' : '/tools')

  // 菜单数据结构
  const menuCategories = getMenuCategories(isAiPage, t)

  // 当切换类别时，重置预览项为该类别的第一项
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category)
    setPreviewItem(null)
  }

  // 导航处理函数
  const handleNavigation = useCallback(() => {
    setIsHovered(false)
    setIsMobileMenuOpen(false)
    // router.push(href)
  }, [router])

  // 获取当前应该显示的预览项
  const getCurrentPreviewItem = () => {
    if (previewItem) return previewItem

    const category =
      menuCategories[activeCategory as keyof typeof menuCategories]
    if (!category) return null

    const filteredItems = category.items.filter((item: any) => !item.hidden)

    return filteredItems.length > 0 ? filteredItems[0] : null
  }

  // 当前预览项
  const currentPreview = getCurrentPreviewItem()

  // 移动端菜单切换
  const toggleMobileMenu = () => {
    const newMenuState = !isMobileMenuOpen
    setIsMobileMenuOpen(newMenuState)
    setIsHovered(false)
    // 通知全局状态
    if (typeof window !== 'undefined' && (window as any).setMobileMenuOpen) {
      ;(window as any).setMobileMenuOpen(newMenuState)
    }
  }

  // 防止移动端菜单打开时页面滚动
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isMobileMenuOpen])

  // 键盘支持 - ESC键关闭移动端菜单
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false)
      }
    }

    if (isMobileMenuOpen) {
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isMobileMenuOpen])

  // 菜单布局渲染
  return (
    <span
      className={cn(
        'flex items-center font-semibold text-foreground leading-none',
        className
      )}
    >
      <Link
        href={'/'}
        onClick={() => handleNavigation('/')}
        className="block hover:no-underline active:no-underline flex-shrink-0 cursor-pointer"
      >
        <Image
          src={logo_img}
          className="w-[40px] h-[24px] sm:w-[60px] sm:h-[36px] flex-shrink-0 object-contain"
          alt="AIImageGenerator"
        />
      </Link>

      {withLabel && (
        <div className="flex items-center">
          {/* 移动端菜单按钮 */}
          {isMobile && (
            <button
              onClick={toggleMobileMenu}
              className="lg:hidden ml-2 p-2 text-purple-300 hover:text-purple-200 transition-colors"
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </button>
          )}

          {/* 桌面端菜单触发器 */}
          <div className={cn('relative', isMobile ? 'hidden' : 'block')}>
            <span className="block font-bold relative sm:ml-2">
              <div className="flex items-center">
                <Link
                  href={'/'}
                  className="text-purple-400 hover:text-white text-base tracking-normal hidden lg:inline whitespace-nowrap overflow-hidden text-ellipsis cursor-pointer transition-colors"
                >
                  IMGGen
                </Link>
                {/* <ChevronDown className="sm:ml-1 h-4 w-4 text-purple-300 flex-shrink-0" /> */}
              </div>
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-fuchsia-500 via-purple-500 to-violet-500 opacity-80 hidden lg:block"></span>
            </span>

            {/* 桌面端 Mega Menu */}
            <div
              className={cn(
                'absolute top-[calc(100%+2px)] left-0 mt-2 bg-gray-900/95 backdrop-blur-md rounded-xl overflow-hidden z-[100]',
                'w-[1120px]',
                'transition-all duration-200 transform origin-top',
                'border border-purple-500/20',
                isHovered
                  ? 'opacity-100 visible translate-y-0 shadow-[0_4px_20px_-2px_rgba(0,0,0,0.3),0_0_15px_-3px_rgba(127,50,237,0.3)] border-purple-500/30'
                  : 'opacity-0 invisible translate-y-[-8px] shadow-none'
              )}
            >
              <div className="p-8">
                {/* AI Image Tools Section */}
                <div className="mb-8">
                  <h2 className="text-white text-xl font-semibold mb-6">
                    AI Image Tools
                  </h2>

                  {/* Featured Tools - 4 cards in a row */}
                  <div className="grid grid-cols-4 gap-4 mb-6">
                    {/* AI Image Generator */}
                    <Link
                      href={`/tools/ai-image-generator`}
                      onClick={() => handleNavigation()}
                      className="group relative bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-4 cursor-pointer hover:shadow-lg hover:shadow-purple-500/20 transition-all"
                    >
                      <div className="absolute top-3 left-3">
                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          Free
                        </span>
                      </div>
                      <div className="pt-8">
                        <h3 className="text-white font-medium text-lg mb-2">
                          AI Image Generator
                        </h3>
                        <p className="text-white/80 text-sm">
                          Generate stunning images from text
                        </p>
                      </div>
                    </Link>

                    {/* Face Swap Photo */}
                    <Link
                      href={'/tools/face-swap'}
                      onClick={() => handleNavigation()}
                      className="group relative bg-gradient-to-r from-green-600 to-blue-600 rounded-xl p-4 cursor-pointer hover:shadow-lg hover:shadow-purple-500/20 transition-all"
                    >
                      <div className="absolute top-3 left-3">
                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          Free
                        </span>
                      </div>
                      <div className="pt-8">
                        <h3 className="text-white font-medium text-lg mb-2">
                          Face Swap Photo
                        </h3>
                        <p className="text-white/80 text-sm">
                          Swap faces in any photo
                        </p>
                      </div>
                    </Link>

                    {/* AI Headshot Generator */}
                    <Link
                      href={'/tools/ai-headshot-generator'}
                      onClick={() => handleNavigation()}
                      className="group relative bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-4 cursor-pointer hover:shadow-lg hover:shadow-purple-500/20 transition-all"
                    >
                      <div className="absolute top-3 left-3">
                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          Free
                        </span>
                      </div>
                      <div className="pt-8">
                        <h3 className="text-white font-medium text-lg mb-2">
                          AI Headshot Generator
                        </h3>
                        <p className="text-white/80 text-sm">
                          Create pro headshots instantly
                        </p>
                      </div>
                    </Link>

                    {/* AI Selfie Generator */}
                    <Link
                      href={'/tools/ai-selfie-generator'}
                      onClick={() => handleNavigation()}
                      className="group relative bg-gradient-to-r from-pink-600 to-purple-600 rounded-xl p-4 cursor-pointer hover:shadow-lg hover:shadow-purple-500/20 transition-all"
                    >
                      <div className="absolute top-3 left-3">
                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          Free
                        </span>
                      </div>
                      <div className="pt-8">
                        <h3 className="text-white font-medium text-lg mb-2">
                          AI Selfie Generator
                        </h3>
                        <p className="text-white/80 text-sm">
                          Stylize photos with AI
                        </p>
                      </div>
                    </Link>
                  </div>
                </div>

                {/* Additional Image Tools Section */}
                <div>
                  <h2 className="text-white text-xl font-semibold mb-6">
                    Additional Image Tools
                  </h2>

                  {/* Grid of additional tools */}
                  <div className="grid grid-cols-4 gap-4">
                    {/* 合并所有分类的工具，排除 coming: true 的项目，展示全部 */}
                    {Object.entries(menuCategories)
                      .flatMap(([categoryKey, category]) =>
                        category.items
                          .filter(
                            (item: MenuItem) => !item.hidden && !item.coming
                          )
                          .map((item: MenuItem) => ({
                            ...item,
                            categoryKey,
                          }))
                      )
                      .map(
                        (
                          item: MenuItem & { categoryKey: string },
                          index: number
                        ) => (
                          <Link
                            href={item.href}
                            key={index}
                            onClick={() => handleNavigation()}
                            className="group flex items-center gap-3 p-4 rounded-lg hover:bg-gray-800/60 transition-all cursor-pointer"
                          >
                            <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
                              {item.icon || getToolIcon(item.categoryKey)}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="text-white font-medium text-sm">
                                  {item.title}
                                </h3>
                                <span className="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full">
                                  Free
                                </span>
                              </div>
                            </div>
                          </Link>
                        )
                      )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 移动端全屏菜单 */}
          {isMobile && (
            <div
              className={cn(
                'fixed inset-0 bg-gray-900 backdrop-blur-md z-[9999] transition-all duration-300 ease-out',
                isMobileMenuOpen
                  ? 'opacity-100 visible translate-y-0'
                  : 'opacity-0 invisible translate-y-4 pointer-events-none'
              )}
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                width: '100vw',
                height: '100vh',
                zIndex: 9999,
              }}
            >
              {/* 背景点击关闭 */}
              <div
                className="absolute inset-0 z-0"
                onClick={toggleMobileMenu}
              />

              {/* 移动端菜单头部 */}
              <div className="flex items-center justify-between p-4 border-b border-purple-500/20 relative z-30">
                <div className="flex items-center">
                  <Image
                    src={logo_img}
                    className="w-8 h-5 flex-shrink-0 object-contain"
                    alt="AIImageGenerator"
                  />
                  <span className="ml-2 text-purple-400 font-bold text-sm">
                    IMGGen
                  </span>
                </div>
                <button
                  onClick={toggleMobileMenu}
                  className="p-2 rounded-lg bg-gray-800/60 border border-purple-500/20 hover:bg-gray-700/60 transition-colors"
                >
                  <X className="h-5 w-5 text-purple-300" />
                </button>
              </div>

              {/* 移动端菜单内容 */}
              <div className="flex flex-col h-full overflow-hidden animate-in slide-in-from-bottom-4 duration-300 relative z-20">
                {/* 分类标签栏 */}
                <div className="flex-shrink-0 flex overflow-x-auto px-4 py-3 border-b border-purple-500/20 bg-gray-800/60 scrollbar-hide">
                  <div className="flex space-x-2 min-w-max">
                    {Object.entries(menuCategories).map(([key, category]) => (
                      <button
                        key={key}
                        className={cn(
                          'px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 flex items-center gap-2',
                          activeCategory === key
                            ? 'bg-gradient-to-r from-purple-900/60 to-fuchsia-900/60 text-white shadow-md shadow-purple-500/20'
                            : 'text-gray-300 hover:bg-gray-700/60'
                        )}
                        onClick={() => handleCategoryChange(key)}
                      >
                        <div className="w-4 h-4 flex items-center justify-center">
                          {getToolIcon(key)}
                        </div>
                        {category.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 工具列表 */}
                <div className="flex-1 overflow-y-auto p-4 scrollbar-hide min-h-0">
                  <div className="space-y-3">
                    {(
                      menuCategories[
                        activeCategory as keyof typeof menuCategories
                      ]?.items || []
                    )
                      .filter((item: MenuItem) => !item.hidden)
                      .map((item: MenuItem, index: number) => {
                        const content = (
                          <div className="flex gap-3 items-start">
                            <ItemIcon
                              item={item}
                              activeCategory={activeCategory}
                            />
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center flex-wrap gap-y-1">
                                <h3 className="font-medium text-gray-200 mr-1 transition-colors">
                                  {item.title}
                                </h3>
                                {item.isHot && <HotLabel />}
                                {item.isNew && <NewLabel />}
                                {item.coming && <ComingLabel />}
                              </div>
                              <p className="text-sm text-gray-400 mt-1">
                                {item.desc}
                              </p>
                            </div>
                          </div>
                        )

                        return (
                          <div
                            key={index}
                            onClick={() => handleNavigation()}
                            className={`p-4 rounded-xl bg-gray-800/40 border border-gray-700/50 hover:bg-gray-700/60 hover:border-purple-500/30 transition-all cursor-pointer active:scale-95 touch-manipulation
                                ${
                                  item.coming
                                    ? 'cursor-not-allowed pointer-events-none opacity-60'
                                    : ''
                                }`}
                          >
                            <Link href={item.href}>
                              {content}
                              {/* 移动端触摸指示器 */}
                              <div className="mt-2 flex items-center justify-end opacity-0 group-hover:opacity-100 transition-opacity">
                                <ChevronRight className="h-4 w-4 text-purple-400" />
                              </div>
                            </Link>
                          </div>
                        )
                      })}
                  </div>

                  {/* 移动端底部资源链接 */}
                  <div className="mt-6 pt-6 border-t border-purple-500/20">
                    <Link
                      href="/guides/getting-started"
                      onClick={() => handleNavigation()}
                    >
                      <div className="p-4 rounded-xl bg-gradient-to-r from-purple-900/60 to-fuchsia-900/60 border border-purple-500/20 active:scale-95 transition-all cursor-pointer">
                        <div className="text-sm font-medium text-purple-200">
                          {t('tools.aiGuide')}
                        </div>
                        <div className="text-xs text-gray-300 mt-1">
                          {t('tools.learnMaximize')}
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </span>
  )
}
