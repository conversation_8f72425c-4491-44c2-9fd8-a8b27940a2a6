'use client'

import type { PropsWithChildren } from 'react'
import { useEffect } from 'react'
import { googleOneTap } from '@/utils/google-one-tap-login'
import {
  getGoogleOneTapConfig,
  isGoogleOneTapSupported,
  getGoogleOneTapErrorMessage,
} from '@/utils/google-one-tap-config'

export function GoogleOneTapProvider({ children }: PropsWithChildren) {
  useEffect(() => {
    if (typeof window === 'undefined') return

    // 检查环境是否支持Google One Tap
    if (!isGoogleOneTapSupported()) {
      return
    }

    // 获取配置
    const config = getGoogleOneTapConfig()
    if (!config) {
      console.error('Failed to get Google One Tap configuration')
      return
    }

    try {
      // googleOneTap(config, (response: any) => {
      //   console.log('Google One Tap response:', response)
      //   // 这里可以处理登录成功的回调
      //   // 可以发送到后端API进行验证
      // })
    } catch (error) {
      const errorMessage = getGoogleOneTapErrorMessage(error)
      console.error('Failed to initialize Google One Tap:', errorMessage)
    }
  }, [])

  return <>{children}</>
}
