'use client'

import { useAtom } from 'jotai'
import { themeAtom, syncThemeAtom } from '@marketing/stores'
import { useEffect } from 'react'
import { getClientTheme, applyThemeToDOM } from '../../../lib/theme-client'

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme] = useAtom(themeAtom)
  const [, syncTheme] = useAtom(syncThemeAtom)

  useEffect(() => {
    // 客户端初始化时从 cookie 同步主题
    const cookieTheme = getClientTheme()
    console.log(
      `🎨 ThemeProvider: 初始化 - atom主题: ${theme}, cookie主题: ${cookieTheme}`
    )

    // 如果 cookie 主题与 atom 主题不同，同步到 cookie 主题
    if (cookieTheme !== theme) {
      console.log(
        `🎨 ThemeProvider: 从 cookie 同步主题 ${theme} -> ${cookieTheme}`
      )
      syncTheme(cookieTheme)
    }

    // 确保 DOM 应用了正确的主题（防止服务端脚本失效）
    applyThemeToDOM(cookieTheme)
  }, []) // 只在组件挂载时执行一次

  useEffect(() => {
    // 当主题变化时，应用到 DOM
    console.log('🎨 ThemeProvider: 应用主题到 DOM ->', theme)
    applyThemeToDOM(theme)
  }, [theme])

  // 直接渲染 children，主题同步在 useEffect 中处理
  // 这样既保证了 SSR，又避免了 hydration 错误
  return <>{children}</>
}
