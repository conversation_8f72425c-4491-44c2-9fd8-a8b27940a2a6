'use client'

import { useAtom } from 'jotai'
import { themeAtom, syncThemeAtom } from '@marketing/stores'
import { useEffect, useState } from 'react'
import { getClientTheme, applyThemeToDOM } from '../../../lib/theme-client'

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme] = useAtom(themeAtom)
  const [, syncTheme] = useAtom(syncThemeAtom)
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // 标记为已水合，避免 hydration 错误
    setIsHydrated(true)

    // 初始化时从 cookie 同步主题
    const cookieTheme = getClientTheme()
    console.log(
      `🎨 ThemeProvider: 初始化 - atom主题: ${theme}, cookie主题: ${cookieTheme}`
    )

    // 总是同步到 cookie 中的主题
    if (cookieTheme !== theme) {
      console.log(
        `🎨 ThemeProvider: 从 cookie 同步主题 ${theme} -> ${cookieTheme}`
      )
      syncTheme(cookieTheme)
    }

    // 确保 DOM 应用了正确的主题
    applyThemeToDOM(cookieTheme)
  }, []) // 只在组件挂载时执行一次

  useEffect(() => {
    // 当主题变化时，应用到 DOM
    if (isHydrated) {
      console.log('🎨 ThemeProvider: 应用主题到 DOM ->', theme)
      applyThemeToDOM(theme)
    }
  }, [theme, isHydrated])

  // 在 hydration 完成前，不渲染任何可能导致不一致的内容
  if (!isHydrated) {
    return <>{children}</>
  }

  return <>{children}</>
}
