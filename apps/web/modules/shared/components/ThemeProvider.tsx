'use client'

import { useAtom } from 'jotai'
import { themeAtom, syncThemeAtom } from '@marketing/stores'
import { useEffect } from 'react'
import { getClientTheme, applyThemeToDOM } from '../../../lib/theme-client'

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme] = useAtom(themeAtom)
  const [, syncTheme] = useAtom(syncThemeAtom)

  useEffect(() => {
    // 初始化时从 cookie 同步主题
    const cookieTheme = getClientTheme()
    if (cookieTheme !== theme) {
      console.log(
        `🎨 ThemeProvider: 从 cookie 同步主题 ${theme} -> ${cookieTheme}`
      )
      syncTheme(cookieTheme)
    }

    // 应用主题到 DOM
    applyThemeToDOM(cookieTheme)
  }, [])

  useEffect(() => {
    // 当主题变化时，应用到 DOM
    if (typeof window !== 'undefined') {
      console.log('🎨 ThemeProvider: 应用主题到 DOM ->', theme)
      applyThemeToDOM(theme)
    }
  }, [theme])

  return <>{children}</>
}
