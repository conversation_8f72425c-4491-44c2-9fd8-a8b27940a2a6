import { atom } from 'jotai'
import {
  getClientTheme,
  setClientTheme,
  toggleClientTheme,
} from '../../../lib/theme-client'

export type Theme = 'light' | 'dark'

// 主题 atom - 从客户端 cookie 初始化
export const themeAtom = atom<Theme>(
  // getter: 从客户端获取当前主题
  () => {
    if (typeof window === 'undefined') {
      return 'dark' // 服务端默认值
    }
    return getClientTheme()
  }
)

// 计算属性：判断是否为亮色主题
export const isLightThemeAtom = atom((get) => get(themeAtom) === 'light')

// 计算属性：判断是否为暗色主题
export const isDarkThemeAtom = atom((get) => get(themeAtom) === 'dark')

// 切换主题的 action atom（用户手动切换）
export const toggleThemeAtom = atom(null, (get, set) => {
  const newTheme = toggleClientTheme()
  set(themeAtom, newTheme)
  return newTheme
})

// 设置主题的 action atom（用户手动设置）
export const setThemeAtom = atom(null, (get, set, newTheme: Theme) => {
  setClientTheme(newTheme, true)
  set(themeAtom, newTheme)
  return newTheme
})

// 同步主题的 action atom（从 cookie 同步，不触发用户偏好）
export const syncThemeAtom = atom(null, (get, set, newTheme: Theme) => {
  setClientTheme(newTheme, false)
  set(themeAtom, newTheme)
  return newTheme
})
