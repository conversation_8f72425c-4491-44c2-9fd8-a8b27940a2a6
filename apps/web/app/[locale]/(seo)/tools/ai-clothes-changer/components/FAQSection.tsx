import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

interface FAQItem {
  question: string
  answer: string
  icon: string
}

export default async function FAQSection({ toolUrl = '' }) {
  const t = await getTranslations('aiClothesChanger')
  const faqs: FAQItem[] = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
      icon: 'fas fa-gift',
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
      icon: 'fas fa-briefcase',
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
      icon: 'fas fa-users',
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
      icon: 'fas fa-magic',
    },
  ]

  return (
    <section className="py-24 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-purple-400">{t('faqTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('faqDescription')}
            <span className="text-purple-400 hover:text-pink-400 cursor-pointer transition-colors">
              {' '}
              {t('contactUs')}
            </span>
            .
          </p>
        </div>

        {/* FAQ List - Now using a CSS-only accordion */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="group overflow-hidden rounded-2xl bg-white/5 border border-transparent hover:border-white/10 transition-colors duration-300"
            >
              {/* Hidden checkbox to control the state */}
              <input
                type="checkbox"
                id={`faq-clothes-${index}`}
                className="absolute opacity-0 peer"
              />

              {/* Label acts as the clickable header */}
              <label
                htmlFor={`faq-clothes-${index}`}
                className="w-full text-left p-6 flex items-center justify-between cursor-pointer group-hover:bg-white/5 transition-colors duration-200"
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <i className={`${faq.icon} text-white text-sm`} />
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold text-white group-hover:text-purple-300 transition-colors">
                    {faq.question}
                  </h3>
                </div>

                {/* Plus/Minus icon swap using CSS opacity */}
                <div className="relative flex-shrink-0 ml-4 w-5 h-5 text-white/60">
                  <i
                    className="fas fa-plus absolute inset-0 transition-opacity duration-300
                               peer-checked:opacity-0"
                  />
                  <i
                    className="fas fa-minus absolute inset-0 opacity-0 transition-opacity duration-300
                               peer-checked:opacity-100"
                  />
                </div>
              </label>

              {/* Answer animates with CSS Grid for smooth height transition */}
              <div
                className="grid transition-all duration-500 ease-in-out
                           grid-rows-[0fr] peer-checked:grid-rows-[1fr]"
              >
                <div className="overflow-hidden">
                  <div className="px-6 pb-6">
                    <div className="pl-14">
                      <p className="text-white/80 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA - AnimatedCard wrapper removed */}
        <div className="mt-16 text-center p-8 rounded-2xl bg-white/5 border border-white/10">
          <div className="text-center space-y-6">
            <h3 className="text-2xl font-bold text-white">
              {t('stillHaveQuestionsTitle')}
            </h3>
            <p className="text-white/80">{t('supportTeamDescription')}</p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link href={toolUrl}>
                <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:scale-105 transition-transform duration-200 flex items-center gap-2">
                  <i className="fas fa-headset" />
                  {t('contactSupportButton')}
                </button>
              </Link>
              <Link href={toolUrl}>
                <button className="px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl hover:bg-white/20 transition-colors duration-200 flex items-center gap-2">
                  <i className="fas fa-book" />
                  {t('viewDocumentationButton')}
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
