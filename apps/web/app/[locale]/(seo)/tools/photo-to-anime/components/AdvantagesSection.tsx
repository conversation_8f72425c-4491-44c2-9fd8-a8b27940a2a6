import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

export default async function AdvantagesSection({
  toolUrl,
}: {
  toolUrl: string
}) {
  const t = await getTranslations()
  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">
              {t('phototoanime.advantagesTitle')}
            </span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('phototoanime.advantagesDescription')}
          </p>
        </div>

        {/* Advantages Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Advantage 1: Authentic Japanese Art Styles - H3 with exact README content */}
          <AnimatedCard delay={100}>
            <div className="text-center md:text-left">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl mb-6">
                <i className="fas fa-palette text-white text-xl" />
              </div>

              <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                {t('phototoanime.advantage1Title')}
              </h3>

              <p className="text-white/80 mb-6 leading-relaxed">
                {t('phototoanime.advantage1Description')}
              </p>

              {/* Feature Icons */}
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2 text-sm text-white/70">
                  <i className="fas fa-check-circle text-green-400" />
                  <span>{t('phototoanime.advantage1Feature1')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-white/70">
                  <i className="fas fa-check-circle text-green-400" />
                  <span>{t('phototoanime.advantage1Feature2')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-white/70">
                  <i className="fas fa-check-circle text-green-400" />
                  <span>{t('phototoanime.advantage1Feature3')}</span>
                </div>
              </div>
            </div>
          </AnimatedCard>

          {/* Advantage 2: True Anime Photo Maker - H3 with exact README content */}
          <AnimatedCard delay={200}>
            <div className="text-center md:text-left">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl mb-6">
                <i className="fas fa-wand-magic-sparkles text-white text-xl" />
              </div>

              <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                {t('phototoanime.advantage2Title')}
              </h3>

              <p className="text-white/80 mb-6 leading-relaxed">
                {t('phototoanime.advantage2Description')}
              </p>

              {/* Feature Icons */}
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2 text-sm text-white/70">
                  <i className="fas fa-check-circle text-green-400" />
                  <span>{t('phototoanime.advantage2Feature1')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-white/70">
                  <i className="fas fa-check-circle text-green-400" />
                  <span>{t('phototoanime.advantage2Feature2')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-white/70">
                  <i className="fas fa-check-circle text-green-400" />
                  <span>{t('phototoanime.advantage2Feature3')}</span>
                </div>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Bottom CTA - Matching README button style */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center gap-4 px-8 py-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-2xl">
            <i className="fas fa-sparkles text-purple-400 text-xl" />
            <span className="text-white font-medium">
              {t('phototoanime.advantagesCta')}
            </span>
            <Link href={toolUrl}>
              <CTAButton>{t('phototoanime.advantagesCtaButton')}</CTAButton>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
