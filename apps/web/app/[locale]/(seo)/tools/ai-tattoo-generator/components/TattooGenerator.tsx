'use client'

import { useState, useEffect, useRef } from 'react'
import { useTranslations } from 'next-intl'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@ui/components/tabs'
import { Button } from '@ui/components/button'
import { Loader2, <PERSON>rkles, Pencil, Download, Check } from 'lucide-react'
import { TATTOO_TEMPLATES } from '../data/tattooTemplates'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { validateUserForGeneration } from '@/utils/userValidation'
import ShowLoginModal from '@shared/components/ShowLoginModal'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
// import PricingSection from '../../(home)/components/PricingSection'
import {
  consumePointsAtom,
  tattooTabAtom,
  tattooPromptAtom,
} from '@marketing/stores'
import { useAtom, useSetAtom } from 'jotai'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'

const STORAGE_KEY = 'tattoo_history'

type User = {
  id: string
  avatar: string
  email: string
  membershipStatus?: string
  points?: string
  balance?: number
}

export default function TattooGenerator({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations()
  const [activeTab, setActiveTab] = useAtom(tattooTabAtom)
  const [prompt, setPrompt] = useAtom(tattooPromptAtom)
  const [isLoading, setIsLoading] = useState(false)
  const [generatedImage, setGeneratedImage] = useState('')
  const [historyImages, setHistoryImages] = useState<string[]>([])
  const [selectMultiple, setSelectMultiple] = useState(false)
  const [selectedImages, setSelectedImages] = useState<string[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [payDialogTitle, setPayDialogTitle] = useState('')
  const userInfoRef = useRef<any>(null)
  const [user, setUser] = useState<User | null>(null)
  const [isClient, setIsClient] = useState(false)
  const consumePoints = useSetAtom(consumePointsAtom)

  const { calculatePoints } = usePermissionCheck()
  const points = calculatePoints({
    taskType: 'tattoo',
    prompt,
  })

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    const clientUser = getUserFromClientCookies() as User | null
    setUser(clientUser)
  }, [])

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedHistory = localStorage.getItem(STORAGE_KEY)
      if (savedHistory) {
        try {
          setHistoryImages(JSON.parse(savedHistory))
        } catch (error) {
          console.error('Error parsing saved history:', error)
        }
      }
    }
  }, [])

  useEffect(() => {
    if (historyImages.length > 0) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(historyImages))
    }
  }, [historyImages])

  const handleTemplateSelect = (templateId: string, templatePrompt: string) => {
    setSelectedTemplate(templateId)
    setPrompt(templatePrompt)
  }

  const handleTemplateEdit = (templatePrompt: string) => {
    setPrompt(templatePrompt)
    setActiveTab('custom')
  }

  const generateTattoo = async () => {
    if (!prompt.trim()) return

    if (!user) {
      setShowLoginModal(true)
      return
    }

    const validationResult: any = await validateUserForGeneration(user, points)
    if (!validationResult.success) {
      setPayDialogTitle(validationResult.message)
      return
    }

    userInfoRef.current = validationResult.userInfo

    setIsLoading(true)
    try {
      const formData = new FormData()
      formData.append('prompt', prompt.trim())

      const createResponse = await fetch('/api/tattoo/create-job', {
        method: 'POST',
        body: formData,
      })

      const createData = await createResponse.json()

      if (createData.code === 401000) {
        setShowLoginModal(true)
        setIsLoading(false)
        return
      }

      if (createData.code !== 100000 || !createData.result?.job_id) {
        throw new Error(
          t('Tattoo.TattooGenerator.generateError') || 'Failed to create job'
        )
      }

      const jobId = createData.result.job_id

      const pollInterval = 2000
      const maxAttempts = 30
      let attempts = 0

      const pollResult = async () => {
        if (attempts >= maxAttempts) {
          throw new Error(t('Tattoo.TattooGenerator.generateTimeout'))
        }

        const statusResponse = await fetch(
          `/api/tattoo/get-job?job_id=${jobId}`
        )
        const statusData = await statusResponse.json()

        if (
          statusData.code === 100000 &&
          statusData.result?.output?.length > 0
        ) {
          const generatedImageUrl = statusData.result.output[0]
          setGeneratedImage(generatedImageUrl)
          setHistoryImages((prev) => [generatedImageUrl, ...prev].slice(0, 24))
          consumePoints(points)
          setIsLoading(false)
          return
        }

        attempts++
        setTimeout(pollResult, pollInterval)
      }

      await pollResult()
    } catch (error) {
      console.error('Error generating tattoo:', error)
      setIsLoading(false)
    }
  }

  const handleSelectAll = () => {
    if (selectedImages.length === historyImages.length) {
      setSelectedImages([])
    } else {
      setSelectedImages([...historyImages])
    }
  }

  const handleSelectImage = (image: string) => {
    if (selectedImages.includes(image)) {
      setSelectedImages(selectedImages.filter((i) => i !== image))
    } else {
      setSelectedImages([...selectedImages, image])
    }
  }

  const handleDownload = async (imageUrl: string) => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()

      const blobUrl = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = blobUrl
      link.download = `tattoo-${Date.now()}.jpg`
      document.body.appendChild(link)
      link.click()

      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
    } catch (error) {
      console.error('Error downloading image:', error)
    }
  }

  return (
    <div className="container" id="tattoo-generator">
      <div className="text-center max-w-4xl mx-auto mb-8 sm:mb-12">
        <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-purple-700 mb-4">
          {t('Tattoo.PageTitle')}
        </h1>
        <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
          {t('Tattoo.PageSubtitle')}
        </p>
      </div>

      <div className="w-full max-w-7xl mx-auto p-4 sm:p-8 bg-white/90 backdrop-blur-sm rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.08)] border border-gray-100/50">
        <section className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          <div className="w-full lg:w-1/2 flex flex-col gap-4">
            <h1 className="text-2xl font-bold text-center mb-4">
              {t('Tattoo.TattooGenerator.title')}
            </h1>

            <Tabs
              value={activeTab}
              // @ts-ignore
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="w-full grid grid-cols-2">
                <TabsTrigger value="idea" className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4" />
                  {t('Tattoo.TattooGenerator.tattooIdea')}
                </TabsTrigger>
                <TabsTrigger value="custom" className="flex items-center gap-2">
                  <Pencil className="w-4 h-4" />
                  {t('Tattoo.TattooGenerator.custom')}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="idea" className="mt-4">
                <div className="grid grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4">
                  {TATTOO_TEMPLATES.map((template) => (
                    <div
                      key={template.id}
                      className="p-2 space-y-1 sm:space-y-2"
                    >
                      <div
                        className={`group relative rounded-lg sm:rounded-xl overflow-hidden cursor-pointer transition-all duration-300 ${
                          selectedTemplate === template.id
                            ? 'ring-2 ring-blue-500 ring-offset-2 scale-105'
                            : 'hover:shadow-lg hover:-translate-y-1'
                        }`}
                        onClick={() =>
                          handleTemplateSelect(template.id, template.prompt)
                        }
                      >
                        <div className="relative aspect-square">
                          <img
                            src={template.image}
                            alt={template.name}
                            className="w-full h-full object-cover"
                          />

                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute bottom-0 left-0 right-0 w-full rounded-none bg-black/40 text-[10px] sm:text-xs font-medium text-white hover:bg-black/50 hover:text-white transition-all"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleTemplateEdit(template.prompt)
                            }}
                          >
                            {t('Tattoo.TattooGenerator.editButton')}
                          </Button>
                        </div>
                      </div>

                      <p className="text-[10px] sm:text-sm font-medium text-center text-gray-700 line-clamp-1">
                        {template.name}
                      </p>
                    </div>
                  ))}
                </div>

                {selectedTemplate && (
                  <div className="mt-4 flex justify-center">
                    <Button
                      className="w-full sm:w-3/4 bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all"
                      disabled={isLoading}
                      onClick={generateTattoo}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />{' '}
                          {t('Tattoo.TattooGenerator.generateLoading')}
                        </>
                      ) : (
                        t('Tattoo.TattooGenerator.generateSelected')
                      )}
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="custom" className="mt-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">
                      {t('Tattoo.TattooGenerator.tattooPrompt')}
                    </label>
                    <div className="mt-1.5 border rounded-xl bg-white overflow-hidden">
                      <textarea
                        placeholder={t(
                          'Tattoo.TattooGenerator.customPromptPlaceholder'
                        )}
                        className="w-full h-40 p-3 border-none focus:outline-none focus:ring-0 bg-transparent resize-y min-h-[160px]"
                        value={prompt}
                        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                          setPrompt(e.target.value)
                        }
                      />
                      <div className="px-3 pb-3">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-purple-500 hover:text-purple-600 hover:bg-purple-50"
                          onClick={() => {
                            const randomTemplate =
                              TATTOO_TEMPLATES[
                                Math.floor(
                                  Math.random() * TATTOO_TEMPLATES.length
                                )
                              ]
                            setPrompt(randomTemplate.prompt)
                          }}
                        >
                          <Sparkles className="w-4 h-4 mr-1" />
                          {t('Tattoo.TattooGenerator.inspiration')}
                        </Button>
                      </div>
                    </div>
                  </div>

                  <Button
                    className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all"
                    disabled={isLoading || !prompt.trim()}
                    onClick={generateTattoo}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />{' '}
                        {t('Tattoo.TattooGenerator.generateLoading')}
                      </>
                    ) : (
                      t('Tattoo.TattooGenerator.generateButton')
                    )}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <div className="w-full lg:w-1/2 flex flex-col gap-4">
            <div className="hidden lg:block text-center mb-4">
              <h2 className="text-xl font-semibold">
                {t('Tattoo.TattooGenerator.generateOnClick')}
              </h2>
            </div>

            <div className="max-w-[480px] mx-auto w-full">
              <div className="relative">
                {isLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg z-10">
                    <div className="flex flex-col items-center gap-4">
                      <div className="relative w-20 h-20">
                        <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-100 rounded-full animate-pulse"></div>
                        <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-500 rounded-full animate-spin [border-top-color:transparent]"></div>
                      </div>
                      <p className="text-sm text-gray-500 animate-pulse">
                        {t('Tattoo.TattooGenerator.generateLoading')}
                      </p>
                    </div>
                  </div>
                )}
                {generatedImage ? (
                  <div className="relative group">
                    <img
                      src={generatedImage}
                      alt={t('Tattoo.TattooGenerator.generatedTattoo')}
                      className="w-auto h-auto max-w-full rounded-lg"
                    />
                    <div className="absolute bottom-2 right-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="bg-black/60 text-white hover:text-white hover:bg-black/80 transition-all duration-200 transform hover:scale-105 hover:shadow-lg"
                        onClick={() => handleDownload(generatedImage)}
                      >
                        <Download className="w-4 h-4 mr-2" />
                        {t('Tattoo.TattooGenerator.download')}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <img
                    src="/tattoo/default.png"
                    alt={t('Tattoo.TattooGenerator.title')}
                    className="w-auto h-auto rounded-lg"
                  />
                )}
              </div>
            </div>

            <div className="text-sm text-gray-500 mb-2">
              <p>{t('Tattoo.TattooGenerator.tips')}</p>
            </div>

            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-medium">
                  {t('Tattoo.TattooGenerator.history')}
                </h3>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0 mb-4">
                <div className="flex items-center gap-4 sm:min-h-[36px]">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="select-multiple"
                      checked={selectMultiple}
                      onChange={(e) => {
                        setSelectMultiple(e.target.checked)
                        if (!e.target.checked) {
                          setSelectedImages([])
                        }
                      }}
                      className="h-4 w-4 rounded border-gray-300 text-blue-500 focus:ring-blue-500/20 focus:ring-offset-0 transition-colors cursor-pointer"
                    />
                    <label
                      htmlFor="select-multiple"
                      className="ml-2 text-sm text-gray-600 cursor-pointer select-none"
                    >
                      {t('Tattoo.TattooGenerator.selectMultiple')}
                    </label>
                  </div>
                  {selectMultiple && isClient && (
                    <button
                      onClick={handleSelectAll}
                      className="text-sm text-blue-500 hover:text-blue-600"
                    >
                      {selectedImages.length === historyImages.length
                        ? t('Tattoo.TattooGenerator.cancel')
                        : t('Tattoo.TattooGenerator.selectAll')}
                    </button>
                  )}
                </div>
                {isClient && selectMultiple && selectedImages.length > 0 && (
                  <div className="flex items-center gap-3 justify-between sm:justify-end">
                    <div className="text-sm text-gray-500 transition-opacity duration-200">
                      {t('Tattoo.TattooGenerator.selected')}:{' '}
                      {selectedImages.length}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1.5 text-purple-500 hover:text-purple-600 border-purple-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 whitespace-nowrap"
                      onClick={() => {
                        selectedImages.forEach((image) => {
                          handleDownload(image)
                        })
                      }}
                    >
                      <Download className="w-4 h-4" />
                      <span className="hidden sm:inline">
                        {t('Tattoo.TattooGenerator.downloadSelected')}
                      </span>
                      <span className="sm:hidden">
                        {t('Tattoo.TattooGenerator.downloadCount', {
                          count: selectedImages.length,
                        })}
                      </span>
                    </Button>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-4 sm:grid-cols-6 gap-2">
                {isClient && historyImages.length > 0 ? (
                  historyImages.map((image, index) => (
                    <div
                      key={index}
                      className={`aspect-square rounded-lg overflow-hidden border bg-white relative group max-w-[100px] 
                          ${selectMultiple ? 'cursor-pointer' : ''} 
                          ${
                            selectMultiple && selectedImages.includes(image)
                              ? 'ring-2 ring-blue-500'
                              : 'border-gray-200'
                          }`}
                      onClick={() => {
                        if (selectMultiple) {
                          handleSelectImage(image)
                        }
                      }}
                    >
                      <img
                        src={image}
                        alt={`History image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                      {selectMultiple && selectedImages.includes(image) && (
                        <div className="absolute top-1.5 left-1.5 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                          <Check className="w-3 h-3 text-white" />
                        </div>
                      )}
                      {!selectMultiple && (
                        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute bottom-1 left-1/2 -translate-x-1/2 text-white hover:text-white hover:bg-white/20 flex items-center gap-1"
                            onClick={() => handleDownload(image)}
                          >
                            <Download className="w-3 h-3" />
                            <span className="text-[10px] font-medium">
                              {t('Tattoo.TattooGenerator.download')}
                            </span>
                          </Button>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="col-span-full text-center text-sm text-gray-500 py-4">
                    {t('Tattoo.TattooGenerator.noHistory')}
                  </div>
                )}
              </div>

              <div className="mt-4 flex items-start gap-2 text-sm text-amber-600 border-t pt-4">
                <span className="i-lucide-warning text-xl"></span>
                <p>{t('Tattoo.TattooGenerator.historyWarning')}</p>
              </div>
            </div>
          </div>
        </section>
      </div>

      {showLoginModal && (
        <ShowLoginModal
          title={t('loginTipsTitle')}
          desc={t('tipLogin')}
          onClose={() => setShowLoginModal(false)}
        />
      )}

      <Dialog
        open={!!payDialogTitle}
        onOpenChange={(open) =>
          setPayDialogTitle((title) => (open ? title : ''))
        }
      >
        <DialogContent
          className="
          max-w-[1200px] 
          w-full 
          h-[920px]
          bg-[#1A1B1E] 
          border-[#2D2E32]
          shadow-xl
        "
        >
          <DialogHeader>
            <DialogTitle className="text-white">
              {t('insufficientCredits')}
            </DialogTitle>
          </DialogHeader>
          <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl p-4">
            <p className="text-center font-semibold text-xl text-purple-300">
              <span className="text-red-500">{payDialogTitle}</span>
            </p>
          </div>
          {/* <PricingSection needTitle={false} className="leading-none" /> */}
        </DialogContent>
      </Dialog>
    </div>
  )
}
