import React from 'react'
import { getTranslations } from 'next-intl/server'
import ImageCarousel from './ImageCarousel'

const CtaSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('Tattoo.CtaSection')

  return (
    <section className="py-24 bg-gradient-to-b from-white to-[#EEF2FF] relative overflow-hidden">
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="h-full w-full"
            style={{
              backgroundImage:
                'linear-gradient(#4F46E5 1px, transparent 1px), linear-gradient(to right, #4F46E5 1px, transparent 1px)',
              backgroundSize: '32px 32px',
            }}
          ></div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* 标题部分 */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold !leading-[1.3] max-w-4xl mx-auto flex flex-col gap-2 pb-2">
            <span className="text-black inline-block">{t('titlePart1')}</span>
            <span className="text-[#2F4BFB] inline-block">
              {t('titlePart2')}
            </span>
          </h2>
        </div>

        {/* 内容部分 */}
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* 左侧图像部分 */}
          <div className="w-full lg:w-1/2">
            <ImageCarousel />
          </div>

          {/* 右侧文字部分 */}
          <div className="w-full lg:w-1/2 text-center lg:text-left">
            <p className="text-xl text-gray-600 mb-10 leading-relaxed max-w-xl">
              {t('description1')}
              <br />
              <br />
              {t('description2')}
              <br />
              <br />
              {t('description3')}
            </p>

            <div className="inline-block mb-10">
              <div className="group relative">
                <a
                  href="/ai-generate-image"
                  className="relative px-10 py-5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white font-medium text-lg inline-block shadow-lg shadow-indigo-600/30 hover:shadow-xl hover:shadow-indigo-600/40 transform hover:-translate-y-1 transition-all duration-300"
                >
                  {t('buttonText')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CtaSection
