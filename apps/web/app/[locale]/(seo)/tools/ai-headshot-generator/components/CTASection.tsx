'use client'
import React from 'react'
import { Camera, Shield, Zap, Image } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const CTASection = ({ link = '' }) => {
  const t = useTranslations('aiHeadshotGenerator')

  const features = [
    {
      icon: <Zap className="w-4 h-4" />,
      text: t('generateInSeconds'),
    },
    {
      icon: <Shield className="w-4 h-4" />,
      text: t('securePrivate'),
    },
    {
      icon: <Image className="w-4 h-4" />,
      text: t('multipleStyles'),
    },
    {
      icon: <Camera className="w-4 h-4" />,
      text: t('highResolution'),
    },
  ]
  return (
    <section className="py-20 bg-gradient-to-br from-pink-900 to-purple-950 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500 rounded-full filter blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-pink-500 rounded-full filter blur-3xl animate-pulse" />
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="text-center">
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            {t('ctaTitle')}
            <span className="block text-purple-400">{t('ctaSubtitle')}</span>
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            {t('ctaDescription')}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={link}
              className="group px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-xl text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20"
            >
              <span className="flex items-center justify-center gap-2">
                <Camera className="w-5 h-5" />
                {t('createFreeHeadshotNow')}
              </span>
            </Link>
          </div>

          <div className="mt-12 flex flex-wrap gap-8 justify-center items-center text-gray-300">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center">
                  {feature.icon}
                </div>
                {feature.text}
              </div>
            ))}
          </div>

          <div className="mt-12 pt-12 border-t border-white/10">
            <div className="flex flex-wrap gap-8 justify-center items-center text-sm text-gray-400">
              <span>{t('noCreditCardRequired')}</span>
              <span>{t('noWatermarks')}</span>
              <span>{t('freeStorageIncluded')}</span>
              <span>{t('commercialUsageRights')}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CTASection
