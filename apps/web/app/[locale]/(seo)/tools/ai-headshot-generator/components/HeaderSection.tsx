'use client'
import React from 'react'
import { Camera, Play } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const HeaderSection = ({ link = '' }) => {
  const t = useTranslations('aiHeadshotGenerator')
  return (
    <section className="min-h-screen py-28 relative bg-gradient-to-br from-purple-950 via-purple-900 to-pink-900 overflow-x-clip">
      {/* Left: Content */}
      <div className="max-w-7xl mx-auto px-4 flex flex-col-reverse lg:flex-row items-center gap-12">
        <div className="flex-1 text-center lg:text-left">
          <div className="mb-6">
            {/* Title Tag for SEO */}
            <div className="sr-only">{t('title')}</div>
            {/* H1 as per SEO requirement */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight">
              {t('heroTitle')}
              <span className="text-purple-400">
                {' '}
                {t('heroTitleHighlight')}{' '}
              </span>
            </h1>
          </div>
          {/* Meta Description for SEO */}
          <div className="sr-only">{t('description')}</div>
          <p className="text-lg sm:text-xl text-gray-200 mb-8 max-w-2xl mx-auto lg:mx-0">
            {t('heroDescription')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
            <Link
              href={link}
              className="group px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-xl text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20"
            >
              <span className="flex items-center justify-center gap-2">
                <Camera className="w-5 h-5" />
                {t('getStartedFree')}
              </span>
            </Link>
            <Link
              href={link + '#examples'}
              className="group px-8 py-4 bg-white/10 text-white font-semibold rounded-xl text-lg transition-all duration-300 hover:bg-white/20 backdrop-blur-sm"
            >
              <span className="flex items-center justify-center gap-2">
                <Play className="w-5 h-5" />
                {t('viewExamples')}
              </span>
            </Link>
          </div>
        </div>
        {/* Right: Image Grid showing the transformation */}
        <div className="flex-1 grid grid-cols-2 gap-4 p-4">
          <div className="space-y-4">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl opacity-0 translate-x-[-100%] animate-slide-in-left">
              <img
                src="/images/ai-headshot-generator/header-1.webp"
                alt={t('professionalHeadshotBefore')}
                className="w-full h-48 object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
            <div className="relative rounded-2xl overflow-hidden shadow-2xl opacity-0 translate-y-[100%] animate-slide-in-bottom">
              <img
                src="/images/ai-headshot-generator/header-2.webp"
                alt={t('professionalHeadshotAfter')}
                className="w-full h-48 object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          </div>
          <div className="space-y-4 mt-8">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl opacity-0 translate-y-[-100%] animate-slide-in-top">
              <img
                src="/images/ai-headshot-generator/header-3.webp"
                alt={t('creativeProfilePictureBefore')}
                className="w-full h-48 object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
            <div className="relative rounded-2xl overflow-hidden shadow-2xl opacity-0 translate-x-[100%] animate-slide-in-right">
              <img
                src="/images/ai-headshot-generator/header-4.webp"
                alt={t('creativeProfilePictureAfter')}
                className="w-full h-48 object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          </div>
        </div>
      </div>

      {/* New ID Photo Showcase Section */}
      <div className="max-w-7xl mx-auto px-4 mt-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('professionalIdPhotos')}
            <span className="text-purple-400"> {t('forAnyPurpose')}</span>
          </h2>
          <p className="text-lg text-gray-200 max-w-2xl mx-auto">
            {t('idPhotosDescription')}
          </p>
        </div>

        <div className="grid translate-x-0 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 justify-items-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-1.png"
              alt="Professional ID photo sample 1"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-2.png"
              alt="Professional ID photo sample 2"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-3.png"
              alt="Professional ID photo sample 3"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-4.png"
              alt="Professional ID photo sample 4"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-5.png"
              alt="Professional ID photo sample 5"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-6.png"
              alt="Professional ID photo sample 6"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-7.png"
              alt="Professional ID photo sample 7"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-8.png"
              alt="Professional ID photo sample 8"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-9.png"
              alt="Professional ID photo sample 9"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 transform hover:scale-105 transition-all duration-300">
            <img
              src="/images/ai-headshot-generator/id-photo-10.png"
              alt="Professional ID photo sample 10"
              className="w-40 h-40 object-cover rounded-lg"
            />
          </div>
        </div>
      </div>

      <div className="absolute inset-0 bg-gradient-to-t from-purple-950/0 to-transparent pointer-events-none"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-purple-950/20 to-transparent pointer-events-none"></div>
    </section>
  )
}

export default HeaderSection
