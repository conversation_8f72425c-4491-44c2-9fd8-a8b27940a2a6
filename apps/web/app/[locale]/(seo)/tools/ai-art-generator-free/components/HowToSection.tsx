import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

interface HowToSectionProps {
  toolUrl: string
}

export default function HowToSection({ toolUrl }: HowToSectionProps) {
  const t = useTranslations('aiArtGeneratorFree')
  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-4xl mx-auto">
        {/* Section Header - H2 with exact SEO content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('howToTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-2xl mx-auto">
            {t('howToDescription')}
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Step 1 */}
          <AnimatedCard delay={100}>
            <div className="text-center h-full flex flex-col">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full text-white text-2xl font-bold mb-6">
                1
              </div>

              <h3 className="text-xl md:text-2xl font-bold text-white mb-4">
                {t('howToStep1Title')}
              </h3>

              <p className="text-white/80 leading-relaxed mb-6 flex-grow">
                {t('howToStep1Description')}
              </p>

              <div className="mt-auto p-4 bg-white/5 border border-white/10 rounded-xl">
                <i className="fas fa-cloud-upload-alt text-purple-400 text-3xl mb-2" />
                <p className="text-sm text-white/60">
                  {t('howToStep1Support')}
                </p>
              </div>
            </div>
          </AnimatedCard>

          {/* Step 2 */}
          <AnimatedCard delay={200}>
            <div className="text-center h-full flex flex-col">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-600 rounded-full text-white text-2xl font-bold mb-6">
                2
              </div>

              <h3 className="text-xl md:text-2xl font-bold text-white mb-4">
                {t('howToStep2Title')}
              </h3>

              <p className="text-white/80 leading-relaxed mb-6 flex-grow">
                {t('howToStep2Description')}
              </p>

              <div className="mt-auto p-4 bg-white/5 border border-white/10 rounded-xl">
                <i className="fas fa-palette text-pink-400 text-3xl mb-2" />
                <p className="text-sm text-white/60">
                  {t('howToStep2Support')}
                </p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Process Visualization */}
        <div className="hidden md:flex items-center justify-center gap-4 mb-12">
          <div className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white text-sm">
            <i className="fas fa-upload text-purple-400" />
            <span>{t('howToProcessUpload')}</span>
          </div>
          <i className="fas fa-arrow-right text-white/40" />
          <div className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white text-sm">
            <i className="fas fa-magic text-pink-400" />
            <span>{t('howToProcessAi')}</span>
          </div>
          <i className="fas fa-arrow-right text-white/40" />
          <div className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white text-sm">
            <i className="fas fa-download text-green-400" />
            <span>{t('howToProcessDownload')}</span>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <CTAButton
            size="lg"
            className="bg-gradient-to-r from-[#5A67D8] to-[#3C366B] hover:from-[#4C51BF] hover:to-[#2D2748]"
            href={toolUrl}
          >
            {t('howToCtaButton')}
          </CTAButton>
        </div>
      </div>
    </section>
  )
}
