import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

interface UseCasesSectionProps {
  toolUrl: string
}

export default function UseCasesSection({ toolUrl }: UseCasesSectionProps) {
  const t = useTranslations('aiArtGeneratorFree')
  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact SEO content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('useCasesTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('useCasesDescription')}
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="space-y-16">
          {/* Use Case 1: Portrait Painting */}
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <AnimatedCard delay={100}>
              <div className="relative">
                <img
                  src="/images/ai-art-generator-free/Snipaste_2025-06-29_15-36-01.png"
                  alt={t('useCase1Alt')}
                  className="w-full rounded-2xl"
                />
              </div>
            </AnimatedCard>

            <AnimatedCard delay={200}>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                  {t('useCase1Title')}
                </h3>
                <p className="text-white/80 mb-6 leading-relaxed">
                  {t('useCase1Description')}
                </p>
                <CTAButton
                  className="bg-[#5A67D8] hover:bg-[#4C51BF]"
                  href={toolUrl}
                >
                  {t('useCase1Button')}
                </CTAButton>
              </div>
            </AnimatedCard>
          </div>

          {/* Use Case 2: Line Drawing */}
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <AnimatedCard delay={100} className="lg:order-2">
              <div className="relative">
                <img
                  src="/images/ai-art-generator-free/Snipaste_2025-06-29_15-41-16.png"
                  alt={t('useCase2Alt')}
                  className="w-full rounded-2xl"
                />
              </div>
            </AnimatedCard>

            <AnimatedCard delay={200} className="lg:order-1">
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                  {t('useCase2Title')}
                </h3>
                <p className="text-white/80 mb-6 leading-relaxed">
                  {t('useCase2Description')}
                </p>
                <CTAButton
                  className="bg-[#5A67D8] hover:bg-[#4C51BF]"
                  href={toolUrl}
                >
                  {t('useCase2Button')}
                </CTAButton>
              </div>
            </AnimatedCard>
          </div>

          {/* Use Case 3: Watercolor */}
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <AnimatedCard delay={100}>
              <div className="relative">
                <img
                  src="/images/ai-art-generator-free/Snipaste_2025-06-29_15-49-35.png"
                  alt={t('useCase3Alt')}
                  className="w-full rounded-2xl"
                />
              </div>
            </AnimatedCard>

            <AnimatedCard delay={200}>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                  {t('useCase3Title')}
                </h3>
                <p className="text-white/80 mb-6 leading-relaxed">
                  {t('useCase3Description')}
                </p>
                <CTAButton
                  className="bg-[#5A67D8] hover:bg-[#4C51BF]"
                  href={toolUrl}
                >
                  {t('useCase3Button')}
                </CTAButton>
              </div>
            </AnimatedCard>
          </div>

          {/* Use Case 4: Western Art */}
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <AnimatedCard delay={100} className="lg:order-2">
              <div className="relative">
                <img
                  src="/images/ai-art-generator-free/Snipaste_2025-06-29_15-52-03.png"
                  alt={t('useCase4Alt')}
                  className="w-full rounded-2xl"
                />
              </div>
            </AnimatedCard>

            <AnimatedCard delay={200} className="lg:order-1">
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                  {t('useCase4Title')}
                </h3>
                <p className="text-white/80 mb-6 leading-relaxed">
                  {t('useCase4Description')}
                </p>
                <CTAButton
                  className="bg-[#5A67D8] hover:bg-[#4C51BF]"
                  href={toolUrl}
                >
                  {t('useCase4Button')}
                </CTAButton>
              </div>
            </AnimatedCard>
          </div>
        </div>
      </div>
    </section>
  )
}
