/* AI Text to Image Page Custom Styles */

/* 自定义动画 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 浮动动画类 */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* 渐变文本 - 修改为纯色以提高兼容性 */
.gradient-text {
  color: #3b82f6; /* 使用渐变终点颜色 blue-500 */
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #a855f7, #ec4899);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9333ea, #db2777);
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(30, 41, 59, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 悬浮卡片效果 */
.hover-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(168, 85, 247, 0.2);
}

/* 脉冲边框效果 */
.pulse-border {
  position: relative;
  overflow: hidden;
}

.pulse-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(45deg, #a855f7, #ec4899, #3b82f6, #a855f7);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: pulse-border 2s linear infinite;
}

@keyframes pulse-border {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 文本打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 2px solid #a855f7;
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: #a855f7;
  }
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-center {
    text-align: center;
  }

  .mobile-full {
    width: 100%;
  }

  .mobile-stack {
    flex-direction: column;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    color-scheme: dark;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .high-contrast {
    border-width: 2px;
    font-weight: 600;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .respect-motion-preference {
    animation: none;
    transition: none;
  }
}

/* 自定义选择样式 */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(168, 85, 247, 0.3);
  color: white;
}

/* 焦点样式 */
.focus-visible:focus-visible {
  outline: 2px solid #a855f7;
  outline-offset: 2px;
}

/* 加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}
