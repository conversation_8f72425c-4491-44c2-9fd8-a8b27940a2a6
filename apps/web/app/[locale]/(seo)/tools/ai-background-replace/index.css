.bg-flow-cyan {
  background: linear-gradient(
    135deg,
    #06b6d4 0%,
    #3b82f6 25%,
    #6366f1 50%,
    #8b5cf6 75%,
    #06b6d4 100%
  );
  background-size: 300% 300%;
  animation: flow-cyan 4s ease-in-out infinite;
  transition: all 0.3s ease;
}

.bg-flow-cyan:hover {
  background-size: 300% 300%;
  animation: flow-cyan 2s ease-in-out infinite;
  box-shadow: 0 0 30px rgba(6, 182, 212, 0.4);
  transform: scale(1.05) translateY(-2px);
}

@keyframes flow-cyan {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.bg-aurora {
  background: linear-gradient(
    45deg,
    rgba(6, 182, 212, 0.1) 0%,
    rgba(59, 130, 246, 0.1) 25%,
    rgba(99, 102, 241, 0.1) 50%,
    rgba(139, 92, 246, 0.1) 75%,
    rgba(6, 182, 212, 0.1) 100%
  );
  background-size: 400% 400%;
  animation: aurora 8s ease-in-out infinite;
}

@keyframes aurora {
  0%,
  100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glow-text {
  text-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
}

.hover-glow:hover {
  box-shadow: 0 0 40px rgba(6, 182, 212, 0.3);
  transform: translateY(-5px);
  transition: all 0.3s ease;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.step-counter {
  color: #06b6d4;
  font-weight: bold;
}
