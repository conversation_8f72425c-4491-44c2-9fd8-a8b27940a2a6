'use client'

import { use<PERSON><PERSON> } from 'jotai'
import { themeAtom, toggleThemeAtom } from '@marketing/stores'
import { getClientTheme } from '../../../lib/theme-client'
import { useEffect, useState } from 'react'

export default function ThemeTestPage() {
  const [theme] = useAtom(themeAtom)
  const [, toggleTheme] = useAtom(toggleThemeAtom)
  const [cookieTheme, setCookieTheme] = useState<string>('')
  const [domTheme, setDomTheme] = useState<string>('')

  useEffect(() => {
    // 获取 cookie 中的主题
    const cookie = getClientTheme()
    setCookieTheme(cookie)
    
    // 获取 DOM 中的主题
    const domClasses = document.documentElement.classList
    const currentDomTheme = domClasses.contains('light') ? 'light' : 
                           domClasses.contains('dark') ? 'dark' : 'none'
    setDomTheme(currentDomTheme)
  }, [theme])

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">主题测试页面</h1>
        
        <div className="space-y-4 mb-8">
          <div className="p-4 border rounded">
            <h2 className="text-xl font-semibold mb-2">主题状态</h2>
            <ul className="space-y-2">
              <li><strong>Atom 主题:</strong> {theme}</li>
              <li><strong>Cookie 主题:</strong> {cookieTheme}</li>
              <li><strong>DOM 主题:</strong> {domTheme}</li>
            </ul>
          </div>
          
          <div className="p-4 border rounded">
            <h2 className="text-xl font-semibold mb-2">当前路径</h2>
            <p>{typeof window !== 'undefined' ? window.location.pathname : 'SSR'}</p>
          </div>
        </div>
        
        <button
          onClick={() => toggleTheme()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          切换主题
        </button>
        
        <div className="mt-8 p-4 border rounded">
          <h2 className="text-xl font-semibold mb-2">主题效果测试</h2>
          <div className="space-y-2">
            <div className="p-2 bg-background text-foreground border">
              背景色和前景色测试
            </div>
            <div className="p-2 bg-primary text-primary-foreground">
              主色调测试
            </div>
            <div className="p-2 bg-secondary text-secondary-foreground">
              次要色调测试
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
