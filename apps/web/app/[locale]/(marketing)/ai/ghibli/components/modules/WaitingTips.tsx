'use client'

import { useEffect, useRef, useState } from 'react'
import { Info, Lightbulb, Sparkles } from 'lucide-react'

// 添加更精致的CSS动画类
const fadeInAnimationStyles = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.3); }
    70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
  }
  
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-4px); }
    100% { transform: translateY(0px); }
  }
  
  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
  
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }
  
  .animate-pulse-subtle {
    animation: pulse 2s infinite;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .shimmer-bg {
    background: linear-gradient(90deg, 
      rgba(59, 130, 246, 0.1) 25%, 
      rgba(99, 102, 241, 0.2) 50%, 
      rgba(59, 130, 246, 0.1) 75%);
    background-size: 200% 100%;
    animation: shimmer 3s linear infinite;
  }
`

interface WaitingTipsProps {
  isGenerating: boolean
  tipsTitle: string
  tipsDescription: string
  buttonText: string
  onExamplesClick: () => void
}

export default function WaitingTips({
  isGenerating,
  tipsTitle,
  tipsDescription,
  buttonText,
  onExamplesClick,
}: WaitingTipsProps) {
  const [showTips, setShowTips] = useState(false)
  const startTimeRef = useRef<number | null>(null)

  // 跟踪生成时间，显示提示
  useEffect(() => {
    if (isGenerating) {
      if (!startTimeRef.current) {
        startTimeRef.current = Date.now()
      }

      const timer = setTimeout(() => {
        if (isGenerating && Date.now() - (startTimeRef.current || 0) >= 10000) {
          setShowTips(true)
        }
      }, 10000)

      return () => clearTimeout(timer)
    } else {
      startTimeRef.current = null
      setShowTips(false)
    }
  }, [isGenerating])

  if (!showTips) return null

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: fadeInAnimationStyles }} />
      <div className="mt-4 sm:mt-6 md:mt-8 animate-fade-in">
        <div className="relative overflow-hidden p-3 sm:p-4 md:p-5 rounded-lg sm:rounded-xl shadow-md sm:shadow-lg border border-blue-200 bg-gradient-to-r from-blue-50 via-indigo-50 to-blue-50 shimmer-bg">
          {/* 装饰性元素 - 在移动设备上隐藏或缩小 */}
          <div className="absolute -top-6 -right-6 sm:-top-8 sm:-right-8 md:-top-10 md:-right-10 w-12 h-12 sm:w-16 sm:h-16 md:w-24 md:h-24 bg-blue-200 rounded-full opacity-20"></div>
          <div className="absolute -bottom-8 -left-8 sm:-bottom-10 sm:-left-10 md:-bottom-12 md:-left-12 w-16 h-16 sm:w-24 sm:h-24 md:w-32 md:h-32 bg-indigo-200 rounded-full opacity-20"></div>

          {/* 移动端采用竖向布局，桌面端采用横向布局 */}
          <div className="relative flex flex-col sm:flex-row items-center sm:items-start gap-2 sm:gap-3 md:gap-4 z-10">
            <div className="bg-white/80 backdrop-blur-sm p-2 rounded-full shadow-sm animate-float mb-1 sm:mb-0">
              <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-blue-500" />
            </div>

            <div className="flex-1 text-center sm:text-left">
              <h4 className="font-semibold text-blue-800 text-sm sm:text-base md:text-lg mb-1 sm:mb-2 flex items-center justify-center sm:justify-start">
                {tipsTitle}
                <span className="inline-block ml-1 sm:ml-2 h-1 w-1 sm:h-1.5 sm:w-1.5 md:h-2 md:w-2 bg-blue-500 rounded-full animate-pulse-subtle"></span>
              </h4>

              <p className="text-blue-700 text-xs sm:text-sm md:text-base leading-relaxed mb-2 sm:mb-3 md:mb-4">
                {tipsDescription}
              </p>

              <button
                onClick={onExamplesClick}
                className="group w-full sm:w-auto flex items-center justify-center sm:justify-start gap-1 sm:gap-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-md sm:rounded-lg text-xs sm:text-sm font-medium shadow-sm hover:shadow-md transition-all duration-300 hover:from-blue-600 hover:to-indigo-600 transform hover:translate-y-[-2px] touch-manipulation"
              >
                <Lightbulb className="h-3 w-3 sm:h-4 sm:w-4 group-hover:animate-float" />
                <span>{buttonText}</span>
                <svg
                  className="w-3 h-3 sm:w-4 sm:h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
