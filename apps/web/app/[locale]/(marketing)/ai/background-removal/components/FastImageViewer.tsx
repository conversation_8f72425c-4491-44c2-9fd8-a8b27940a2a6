'use client'

import { useState, useCallback, useEffect } from 'react'
import { localImageCache, getOptimizedImageUrl } from '../lib/localImageCache'
import styles from './index.module.css'

interface FastImageViewerProps {
  src: string
  originalSrc?: string | null
  alt: string
  className?: string
  containerClassName?: string
  small?: boolean
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: () => void
  showOriginal?: boolean
}

/**
 * 快速图片查看器，使用本地缓存优化加载速度
 * 对于PNG图片，会自动添加马赛克背景来显示透明区域
 */
export default function FastImageViewer({
  src,
  originalSrc,
  alt,
  className = '',
  containerClassName = '',
  small = false,
  style,
  onLoad,
  onError,
  showOriginal = false,
}: FastImageViewerProps) {
  const [isPNG, setIsPNG] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [optimizedSrc, setOptimizedSrc] = useState(src)
  const [optimizedOriginalSrc, setOptimizedOriginalSrc] = useState<string | null>(null)

  // 检测是否为PNG图片
  const checkIfPNG = useCallback((imageUrl: string) => {
    const urlContainsPNG = imageUrl.toLowerCase().includes('.png')
    const isPossiblePNG = urlContainsPNG || 
      imageUrl.includes('remove.bg') ||
      imageUrl.includes('background-remove') ||
      imageUrl.includes('bg-remove')
    return isPossiblePNG
  }, [])

  // 优化处理后的图片URL
  useEffect(() => {
    const optimizeImage = async () => {
      try {
        const optimized = await localImageCache.cacheImage(src)
        setOptimizedSrc(optimized)
      } catch (error) {
        console.warn('Failed to optimize image:', error)
        setOptimizedSrc(src)
      }
    }

    optimizeImage()
  }, [src])

  // 优化原图URL
  useEffect(() => {
    if (originalSrc) {
      const optimizeOriginal = async () => {
        try {
          const optimized = await localImageCache.cacheImage(originalSrc)
          setOptimizedOriginalSrc(optimized)
        } catch (error) {
          console.warn('Failed to optimize original image:', error)
          setOptimizedOriginalSrc(originalSrc)
        }
      }

      optimizeOriginal()
    }
  }, [originalSrc])

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true)
    setIsPNG(checkIfPNG(src))
    onLoad?.()
  }, [src, checkIfPNG, onLoad])

  const handleImageError = useCallback(() => {
    setImageLoaded(true)
    onError?.()
  }, [onError])

  // 组合CSS类名
  const getContainerClasses = () => {
    const baseClasses = [
      styles.imageWithTransparency,
      containerClassName
    ]

    // 只有当图片已加载且检测为PNG时才添加马赛克背景
    if (imageLoaded && isPNG) {
      baseClasses.push(small ? styles.transparencyCheckerSmall : styles.transparencyChecker)
    }

    return baseClasses.filter(Boolean).join(' ')
  }

  const getImageClasses = () => {
    const baseClasses = [
      'w-full h-full',
      className
    ]

    // 为PNG图片添加特殊样式确保透明度正确显示
    if (imageLoaded && isPNG) {
      baseClasses.push('object-contain')
    } else {
      baseClasses.push('object-cover')
    }

    return baseClasses.filter(Boolean).join(' ')
  }

  // 决定显示哪个图片
  const currentSrc = showOriginal && optimizedOriginalSrc ? optimizedOriginalSrc : optimizedSrc
  const isOriginalReady = showOriginal ? Boolean(optimizedOriginalSrc) : true

  return (
    <div 
      className={getContainerClasses()}
      style={style}
    >
      <img
        src={currentSrc}
        alt={alt}
        className={getImageClasses()}
        onLoad={handleImageLoad}
        onError={handleImageError}
        loading="eager" // 使用eager加载以提高响应速度
        style={{
          opacity: isOriginalReady ? 1 : 0.7,
          transition: 'opacity 0.2s ease-in-out'
        }}
      />
      
      {/* 加载指示器 - 仅在切换到原图但原图未准备好时显示 */}
      {showOriginal && !isOriginalReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg">
          <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  )
}
