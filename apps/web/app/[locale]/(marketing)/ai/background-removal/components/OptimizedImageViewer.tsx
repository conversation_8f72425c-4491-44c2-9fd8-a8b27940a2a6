'use client'

import { useState, useCallback, useEffect, useRef } from 'react'
import { imageCache } from '../lib/imageCache'
import styles from './index.module.css'

interface OptimizedImageViewerProps {
  src: string
  originalSrc?: string | null
  alt: string
  className?: string
  containerClassName?: string
  small?: boolean
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: () => void
  showOriginal?: boolean
}

/**
 * 优化的图片查看器，支持原图预加载和快速切换
 * 对于PNG图片，会自动添加马赛克背景来显示透明区域
 */
export default function OptimizedImageViewer({
  src,
  originalSrc,
  alt,
  className = '',
  containerClassName = '',
  small = false,
  style,
  onLoad,
  onError,
  showOriginal = false,
}: OptimizedImageViewerProps) {
  const [isPNG, setIsPNG] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [originalPreloaded, setOriginalPreloaded] = useState(false)
  const originalImageRef = useRef<HTMLImageElement | null>(null)
  const processedImageRef = useRef<HTMLImageElement | null>(null)

  // 检测是否为PNG图片
  const checkIfPNG = useCallback((imageUrl: string) => {
    const urlContainsPNG = imageUrl.toLowerCase().includes('.png')
    const isPossiblePNG =
      urlContainsPNG ||
      imageUrl.includes('remove.bg') ||
      imageUrl.includes('background-remove') ||
      imageUrl.includes('bg-remove')
    return isPossiblePNG
  }, [])

  // 使用缓存预加载原图
  useEffect(() => {
    if (originalSrc && !originalPreloaded) {
      // 检查是否已缓存
      if (imageCache.has(originalSrc)) {
        setOriginalPreloaded(true)
        originalImageRef.current = imageCache.get(originalSrc)
      } else {
        // 预加载到缓存
        imageCache
          .preload(originalSrc)
          .then((img) => {
            setOriginalPreloaded(true)
            originalImageRef.current = img
          })
          .catch(() => {
            console.warn('Failed to preload original image:', originalSrc)
          })
      }
    }
  }, [originalSrc, originalPreloaded])

  // 使用缓存预加载处理后的图片
  useEffect(() => {
    if (src && !processedImageRef.current) {
      // 检查是否已缓存
      if (imageCache.has(src)) {
        processedImageRef.current = imageCache.get(src)
        setImageLoaded(true)
        setIsPNG(checkIfPNG(src))
        onLoad?.()
      } else {
        // 预加载到缓存
        imageCache
          .preload(src)
          .then((img) => {
            processedImageRef.current = img
            setImageLoaded(true)
            setIsPNG(checkIfPNG(src))
            onLoad?.()
          })
          .catch(() => {
            setImageLoaded(true)
            onError?.()
          })
      }
    }
  }, [src, checkIfPNG, onLoad, onError])

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true)
    setIsPNG(checkIfPNG(src))
    onLoad?.()
  }, [src, checkIfPNG, onLoad])

  const handleImageError = useCallback(() => {
    setImageLoaded(true)
    onError?.()
  }, [onError])

  // 组合CSS类名
  const getContainerClasses = () => {
    const baseClasses = [styles.imageWithTransparency, containerClassName]

    // 只有当图片已加载且检测为PNG时才添加马赛克背景
    if (imageLoaded && isPNG) {
      baseClasses.push(
        small ? styles.transparencyCheckerSmall : styles.transparencyChecker
      )
    }

    return baseClasses.filter(Boolean).join(' ')
  }

  const getImageClasses = () => {
    const baseClasses = ['w-full h-full', className]

    // 为PNG图片添加特殊样式确保透明度正确显示
    if (imageLoaded && isPNG) {
      baseClasses.push('object-contain')
    } else {
      baseClasses.push('object-cover')
    }

    return baseClasses.filter(Boolean).join(' ')
  }

  // 决定显示哪个图片
  const currentSrc = showOriginal && originalSrc ? originalSrc : src
  const shouldShowPreloadedOriginal =
    showOriginal && originalSrc && originalPreloaded

  return (
    <div className={getContainerClasses()} style={style}>
      {/* 主图片 - 始终渲染但通过opacity控制显示 */}
      <img
        src={currentSrc}
        alt={alt}
        className={getImageClasses()}
        onLoad={handleImageLoad}
        onError={handleImageError}
        loading={showOriginal ? 'eager' : 'lazy'}
        style={{
          opacity: shouldShowPreloadedOriginal ? 1 : showOriginal ? 0.7 : 1,
          transition: 'opacity 0.15s ease-in-out',
        }}
      />

      {/* 加载指示器 - 仅在切换到原图但原图未预加载完成时显示 */}
      {showOriginal && originalSrc && !originalPreloaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg">
          <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  )
}
