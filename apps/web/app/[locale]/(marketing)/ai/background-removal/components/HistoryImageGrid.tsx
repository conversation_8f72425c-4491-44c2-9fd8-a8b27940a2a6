'use client'

import { Download } from 'lucide-react'
import { useState, useEffect } from 'react'
import TransparencyImageViewer from './TransparencyImageViewer'
import styles from './index.module.css'

interface HistoryImageGridProps {
  images: string[]
  title: string
  onDownload: (imageUrl: string, index: number) => void
  maxImages?: number
}

export default function HistoryImageGrid({
  images,
  title,
  onDownload,
  maxImages = 4,
}: HistoryImageGridProps) {
  const [imageDimensions, setImageDimensions] = useState<
    { width: number; height: number; aspectRatio: number }[]
  >([])

  // 获取图片尺寸信息
  useEffect(() => {
    const loadImageDimensions = async () => {
      const dimensions = await Promise.all(
        images.map(
          (url) =>
            new Promise<{ width: number; height: number; aspectRatio: number }>(
              (resolve) => {
                const img = new Image()
                img.onload = () => {
                  resolve({
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                    aspectRatio: img.naturalWidth / img.naturalHeight,
                  })
                }
                img.onerror = () => {
                  // 如果图片加载失败，使用默认比例
                  resolve({
                    width: 512,
                    height: 512,
                    aspectRatio: 1,
                  })
                }
                img.src = url
              }
            )
        )
      )
      setImageDimensions(dimensions)
    }

    if (images.length > 0) {
      loadImageDimensions()
    }
  }, [images])

  if (images.length === 0) {
    return null
  }

  // 获取单个图片容器的样式
  const getImageContainerStyle = (index: number) => {
    const dimension = imageDimensions[index]
    if (!dimension) {
      // 如果尺寸还没加载完成，使用默认样式
      return {
        aspectRatio: '1',
        width: '120px',
        height: '120px',
      }
    }

    // 保持原始比例，但限制最大尺寸
    const { aspectRatio } = dimension
    const maxSize = 120 // 统一最大尺寸

    // 计算在保持比例的情况下的实际尺寸
    let width = maxSize
    let height = maxSize / aspectRatio

    if (height > maxSize) {
      height = maxSize
      width = maxSize * aspectRatio
    }

    return {
      aspectRatio: aspectRatio.toString(),
      width: `${width}px`,
      height: `${height}px`,
      flexShrink: 0, // 防止被压缩
    }
  }

  return (
    <div>
      <h4 className="text-sm font-medium text-gray-900 mb-2">
        {title} ({images.length}):
      </h4>
      <div className="flex flex-wrap gap-2 max-w-2xl">
        {images.slice(0, maxImages).map((imageUrl, index) => (
          <div
            key={index}
            className={`relative group border border-gray-200 rounded-md overflow-hidden ${styles.imageContainer}`}
            style={getImageContainerStyle(index)}
          >
            <TransparencyImageViewer
              src={imageUrl}
              alt={`${title} ${index + 1}`}
              className="rounded-md"
              small={true}
            />
            <button
              onClick={() => onDownload(imageUrl, index)}
              className={styles.downloadButtonSmall}
              title="Download"
            >
              <Download className="h-4 w-4" />
            </button>
          </div>
        ))}
      </div>
      {images.length > maxImages && (
        <p className="text-xs text-gray-500 mt-1">
          Showing {maxImages} of {images.length} images
        </p>
      )}
    </div>
  )
}
