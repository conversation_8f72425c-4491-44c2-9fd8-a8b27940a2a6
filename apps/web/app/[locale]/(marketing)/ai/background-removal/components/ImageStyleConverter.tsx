'use client'

import { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, Sparkles } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai'
import { Slider } from '@ui/components/slider'
import { Button } from '@ui/components/button'
import { Form } from '@ui/components/form'
import {
  imagesAtom,
  formDataAtom,
  generatedTaskIdAtom,
  formSchema,
  type FormData,
  generationProgressAtom,
  isGeneratingAtom,
  generationErrorAtom,
  generatedImageUrlAtom,
  generatedImageUrls<PERSON>tom,
  originalImageUrl<PERSON>tom,
  currentTaskIdAtom,
} from '../lib/state'
import { useModal } from '@shared/hooks/useModal'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations } from 'next-intl'
import { consumePoints<PERSON>tom } from '@marketing/stores'
import { uploadToOSS } from '../lib/utils'
import { ImageUploadArea } from './ImageUploadArea'
import { TASK_TYPES } from '../../../../../../constants'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'
import PointIcon from '../../components/ui/PointIcon'

// API related configuration
const API_URL_GENERATE = '/api/aiimage/generate'
const POLL_INTERVAL = 5000
const MAX_POLL_TIME = 600000 // 5 minutes
const DEFAULT_MAX_UPLOAD_IMAGES = 1

type ImageStyleConverterProps = {
  maxUploadImages?: number
}

export function ImageStyleConverter({
  maxUploadImages = DEFAULT_MAX_UPLOAD_IMAGES,
}: ImageStyleConverterProps) {
  const t = useTranslations()
  const router = useRouter()
  // 使用整合的图片数据
  const [images, setImages] = useAtom(imagesAtom)
  const { showLoginModal, showInsufficientCreditsModal } = useModal()
  const [formData, setFormData] = useAtom(formDataAtom)
  const [generatedTaskId, setGeneratedTaskId] = useAtom(generatedTaskIdAtom)

  const user = getUserFromClientCookies()

  const { calculatePoints, updateUserInfo } = usePermissionCheck()

  // 默认表单数据
  const defaultFormValues: FormData = {
    scale: '2',
  }

  // Form configuration - 只使用默认值初始化表单
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  })

  // Consume points
  const consumePoints = useSetAtom(consumePointsAtom)

  // Use global state
  const [isGenerating, setIsGenerating] = useAtom(isGeneratingAtom)
  const [, setGenerationProgress] = useAtom(generationProgressAtom)
  const [generationError, setGenerationError] = useAtom(generationErrorAtom)
  const [, setGeneratedImageUrl] = useAtom(generatedImageUrlAtom)
  const [, setGeneratedImageUrls] = useAtom(generatedImageUrlsAtom)
  const [, setOriginalImageUrl] = useAtom(originalImageUrlAtom)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTaskId, setCurrentTaskId] = useAtom(currentTaskIdAtom)
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)

  const resetLoading = useCallback(() => {
    setIsGenerating(false)
    setGenerationProgress(0)
    setGeneratedTaskId(null)
    setIsSubmitting(false)
    setCurrentTaskId(null)
    setGeneratedImageUrl(null)
    setGeneratedImageUrls([])
  }, [
    setIsGenerating,
    setGenerationProgress,
    setGeneratedTaskId,
    setIsSubmitting,
    setCurrentTaskId,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
  ])

  // Start polling task status using unified PiAPI endpoint
  const startPolling = useCallback(
    (id: string) => {
      // If this task has already been successfully generated, return directly
      if (generatedTaskId === id) {
        setIsSubmitting(false)
        setCurrentTaskId(null)
        return
      }

      // 确保每次开始轮询时都重置开始时间
      startTimeRef.current = Date.now()
      console.log(
        '开始轮询任务:',
        id,
        '开始时间:',
        new Date(startTimeRef.current).toLocaleString()
      )

      const pollForResult = async () => {
        try {
          const response = await fetch(`/api/aiimage/task/${id}`, {
            method: 'GET',
            headers: {
              Accept: 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`)
          }

          const data = await response.json()

          if (data.code === 200 && data.data) {
            const taskData = data.data

            if (taskData.status === 'completed') {
              // Generation successful
              const resultUrl = taskData.output?.image_url || null
              const resultUrls = resultUrl ? [resultUrl] : []

              // 保存所有生成的图片URL
              setGeneratedImageUrls(resultUrls)
              setIsGenerating(false)
              setGenerationProgress(100)
              setGeneratedTaskId(id)

              if (resultUrl) {
                setGeneratedImageUrl(resultUrl)
                console.log('Setting generated image URL:', resultUrl)
              }

              setIsSubmitting(false)
              setCurrentTaskId(null)

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else if (taskData.status === 'failed') {
              // Generation failed
              const errorMessage = `Generation failed: ${
                taskData.error?.message || 'Unknown error'
              }`

              // History record update handled by webhook
              setGenerationError(errorMessage)
              resetLoading()

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else {
              // Still processing (pending or processing)
              const elapsed = Date.now() - (startTimeRef.current || Date.now())

              // Update progress based on status
              if (taskData.status === 'processing') {
                setGenerationProgress(50) // Processing state
              } else {
                setGenerationProgress(25) // Pending state
              }

              // Check if timeout
              if (elapsed > MAX_POLL_TIME) {
                console.log('Generation timed out, please try again')
                setGenerationError(t('ImageStyleConverter.generationTimeout'))
                resetLoading()

                if (pollIntervalRef.current) {
                  clearInterval(pollIntervalRef.current)
                  pollIntervalRef.current = null
                }
              }
            }
          } else {
            throw new Error(data.message || 'Request failed')
          }
        } catch (err) {
          console.error('Error polling for result:', err)
          console.error('Task ID:', id)
          setGenerationError(t('ImageStyleConverter.generationError'))
          resetLoading()

          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current)
            pollIntervalRef.current = null
          }
        }
      }

      // Execute immediately, then set timer
      pollForResult()
      pollIntervalRef.current = setInterval(pollForResult, POLL_INTERVAL)
    },
    [
      setGeneratedTaskId,
      generatedTaskId,
      resetLoading,
      setIsGenerating,
      setGenerationProgress,
      setGenerationError,
      setGeneratedImageUrl,
      setGeneratedImageUrls,
      setCurrentTaskId,
      t,
    ]
  )

  // Check if form is valid (PiAPI upscale supports single image only)
  const isFormValid = useMemo(() => {
    return images.length === 1
  }, [images])

  // Form submission handling
  const onSubmit = useCallback(
    async (values: FormData) => {
      if (!images.length) {
        setGenerationError('Please upload an image')
        return
      }

      if (images.length > 1) {
        setGenerationError('Please upload only one image for upscaling')
        return
      }

      // Check points first
      if (!user) {
        showLoginModal({
          title: t('loginTipsTitle'),
          content: t('tipLogin'),
          props: {
            needBottomArea: true, // 显示会员权益
          },
        })
        return
      }

      // If already submitting or there's an ongoing task, don't submit again
      if (isSubmitting || currentTaskId) {
        return
      }

      setGenerationError(null)
      setIsSubmitting(true)
      setIsGenerating(true)

      // 只有在没有当前任务ID时才重置进度为5%（表示这是一个新任务）
      if (!currentTaskId) {
        setGenerationProgress(5)
      }

      try {
        let filesUrl: string[] = []

        // 收集第一张图片的 OSS URL（PiAPI upscale只支持单张图片）
        const firstImage = images[0]

        if (firstImage.ossUrl) {
          // 图片已上传完成，直接使用 OSS URL
          filesUrl = [firstImage.ossUrl]
          setOriginalImageUrl(firstImage.ossUrl)
        } else if (firstImage.file) {
          // 如果图片还未上传完成，上传图片
          const ossUrl = await uploadToOSS(firstImage.file)
          filesUrl = [ossUrl]
          setOriginalImageUrl(ossUrl)
        } else {
          throw new Error('No valid image found')
        }

        const response = await fetch(API_URL_GENERATE, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify({
            task_type: TASK_TYPES.BACKGROUND_REMOVAL,
            input: {
              image: filesUrl[0],
            },
          }),
        })

        if (!response.ok) {
          console.log(`HTTP error! Status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 401) {
          showLoginModal({
            title: t('loginTipsTitle'),
            content: t('tipLogin'),
          })
          resetLoading()
          return
        }

        if (data.code === 400 && data.message?.includes('Insufficient')) {
          resetLoading()
          showInsufficientCreditsModal({
            content: t('ImageStyleConverter.insufficientPoints'),
          })
          return
        }

        if (data.code === 200) {
          const newTaskId = data.data.task_id
          setCurrentTaskId(newTaskId)

          updateUserInfo()

          startPolling(newTaskId)
        } else {
          throw new Error(data.message || 'Request failed')
        }
      } catch (err) {
        console.error('Generation request failed:', err)
        setGenerationError(
          'Failed to initiate generation request, please try again'
        )
        resetLoading()
      }
    },
    [
      images,
      router,
      setFormData,
      startPolling,
      resetLoading,
      isSubmitting,
      showLoginModal,
      showInsufficientCreditsModal,
      user,
      t,
      consumePoints,
      setOriginalImageUrl,
    ]
  )

  // 包装上传方法以处理错误状态
  const handleUploadToOSS = async (file: File): Promise<string> => {
    try {
      return await uploadToOSS(file)
    } catch (error) {
      console.error('Failed to upload to OSS:', error)
      setGenerationError('Failed to upload image, please try again')
      throw error
    }
  }

  // Listen for form changes
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current)
      }
    }
  }, [])

  return (
    <>
      <div className="w-full h-full image-style-converter">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col h-full"
          >
            <div className="flex-shrink-0 mb-3">
              <ImageUploadArea
                images={images}
                onImagesChange={setImages}
                maxUploadImages={maxUploadImages}
                isGenerating={isGenerating}
                allowDelete={false}
                onUploadStart={async (file: File, index: number) => {
                  try {
                    // 上传文件到 OSS
                    const ossUrl = await handleUploadToOSS(file)

                    // 更新图片数据
                    setImages((prevImages) => {
                      const updatedImages = [...prevImages]
                      if (index < updatedImages.length) {
                        updatedImages[index] = {
                          ...updatedImages[index],
                          ossUrl,
                          uploading: false,
                        }
                      }
                      return updatedImages
                    })
                  } catch (error) {
                    // 更新图片数据，标记错误
                    setImages((prevImages) => {
                      const updatedImages = [...prevImages]
                      if (index < updatedImages.length) {
                        updatedImages[index] = {
                          ...updatedImages[index],
                          uploading: false,
                          error: t('ImageStyleConverter.uploadFailed'),
                        }
                      }
                      return updatedImages
                    })
                    console.error(`Upload failed at index ${index}:`, error)
                  }
                }}
              />
            </div>

            <div className="overflow-y-auto">
              {/* Upscale Size selection */}
              {/* <div className="mb-4">
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-800 mb-2">
                    Upscale Size
                  </h3>
                  <div className="w-full px-2">
                    <Slider
                      value={[
                        form.watch('scale') === '2'
                          ? 0
                          : form.watch('scale') === '4'
                          ? 1
                          : 2,
                      ]}
                      onValueChange={(value) => {
                        // Map slider values to scale values: 0->2, 1->4, 2->8
                        const scaleMap = ['2', '4', '8']
                        form.setValue(
                          'scale',
                          scaleMap[value[0]] as '2' | '4' | '8'
                        )
                      }}
                      disabled={isGenerating}
                      min={0}
                      max={2}
                      step={1}
                      className={`w-full ${
                        isGenerating ? 'opacity-70 cursor-not-allowed' : ''
                      }`}
                    />
                    <div className="flex justify-between text-sm text-gray-500 mt-2">
                      <span>2x</span>
                      <span>4x</span>
                      <span>8x</span>
                    </div>
                  </div>
                </div>
              </div> */}
            </div>

            {/* Generate button and progress */}
            <div className="space-y-4 flex-shrink-0">
              <Button
                type="submit"
                className="w-full bg-gradient-to-r gap-1 from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
                disabled={!isFormValid}
                loading={isGenerating}
                size="lg"
              >
                {isGenerating
                  ? t('ImageStyleConverter.generating')
                  : t('ImageStyleConverter.generateButton')}

                <PointIcon points={calculatePoints()} />
              </Button>

              {generationError && (
                <div
                  className={`bg-red-50 p-3 rounded-md flex items-start ${'bg-opacity-80'}`}
                >
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-600">{generationError}</p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
    </>
  )
}
