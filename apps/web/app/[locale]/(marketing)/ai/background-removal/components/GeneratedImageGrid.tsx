'use client'

import { useTranslations } from 'next-intl'
import { Download, Eye } from 'lucide-react'
import { useState, useEffect, useMemo, useCallback } from 'react'
import FastImageViewer from './FastImageViewer'
import { preloadImageToCache } from '../lib/localImageCache'
import styles from './index.module.css'

interface GeneratedImageGridProps {
  imageUrls: string[]
  originalImageUrl?: string | null
  onDownload: (imageUrl: string, index: number) => void
  onDownloadAll: () => void
}

export default function GeneratedImageGrid({
  imageUrls,
  originalImageUrl,
  onDownload,
  onDownloadAll,
}: GeneratedImageGridProps) {
  const t = useTranslations()
  const [imageDimensions, setImageDimensions] = useState<
    { width: number; height: number; aspectRatio: number }[]
  >([])
  const [showingOriginal, setShowingOriginal] = useState<number | null>(null)

  // 预加载原图
  useEffect(() => {
    if (originalImageUrl) {
      preloadImageToCache(originalImageUrl)
        .then(() => {
          console.log('Original image preloaded successfully')
        })
        .catch((error) => {
          console.warn('Failed to preload original image:', error)
        })
    }
  }, [originalImageUrl])

  // 处理按住显示原图的逻辑
  const handleShowOriginal = useCallback((index: number, show: boolean) => {
    setShowingOriginal(show ? index : null)
  }, [])

  // 添加全局事件监听器来处理松开事件
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setShowingOriginal(null)
    }

    const handleGlobalTouchEnd = () => {
      setShowingOriginal(null)
    }

    if (typeof window !== 'undefined') {
      document.addEventListener('mouseup', handleGlobalMouseUp)
      document.addEventListener('touchend', handleGlobalTouchEnd)
      document.addEventListener('touchcancel', handleGlobalTouchEnd)

      return () => {
        document.removeEventListener('mouseup', handleGlobalMouseUp)
        document.removeEventListener('touchend', handleGlobalTouchEnd)
        document.removeEventListener('touchcancel', handleGlobalTouchEnd)
      }
    }
  }, [])

  // 是否可以显示对比功能
  const canShowComparison = useMemo(() => {
    return imageUrls.length >= 1 && originalImageUrl
  }, [imageUrls, originalImageUrl])

  // 获取图片尺寸信息
  useEffect(() => {
    const loadImageDimensions = async () => {
      const dimensions = await Promise.all(
        imageUrls.map(
          (url) =>
            new Promise<{ width: number; height: number; aspectRatio: number }>(
              (resolve) => {
                const img = new Image()
                img.onload = () => {
                  resolve({
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                    aspectRatio: img.naturalWidth / img.naturalHeight,
                  })
                }
                img.onerror = () => {
                  // 如果图片加载失败，使用默认比例
                  resolve({
                    width: 512,
                    height: 512,
                    aspectRatio: 1,
                  })
                }
                img.src = url
              }
            )
        )
      )
      setImageDimensions(dimensions)
    }

    if (imageUrls.length > 0) {
      loadImageDimensions()
    }
  }, [imageUrls])

  if (imageUrls.length === 0) {
    return null
  }

  // 根据图片数量决定网格布局
  const getGridClasses = () => {
    switch (imageUrls.length) {
      case 1:
        return 'grid-cols-1 max-w-md mx-auto'
      case 2:
        return 'grid-cols-2 gap-3 max-w-xl'
      case 4:
        return 'grid-cols-2 gap-3 max-w-xl'
      default:
        return 'grid-cols-2 sm:grid-cols-3 gap-3 max-w-xl'
    }
  }

  // 获取单个图片容器的样式
  const getImageContainerStyle = (index: number) => {
    const dimension = imageDimensions[index]
    if (!dimension) {
      // 如果尺寸还没加载完成，使用默认样式
      return {
        aspectRatio: '1',
        maxWidth: imageUrls.length === 1 ? '100%' : '200px',
      }
    }

    // 保持原始比例
    const { aspectRatio } = dimension
    return {
      aspectRatio: aspectRatio.toString(),
      maxWidth: imageUrls.length === 1 ? '100%' : '200px',
    }
  }

  return (
    <div className="w-full max-w-3xl flex flex-col items-center justify-center">
      {/* 图片网格视图 */}
      <div className={`w-full grid ${getGridClasses()}`}>
        {imageUrls.map((imageUrl, index) => (
          <div
            key={index}
            className={`relative group flex items-center justify-center rounded-lg overflow-hidden ${styles.imageContainer}`}
            style={getImageContainerStyle(index)}
          >
            {/* 显示的图片：使用快速图片查看器 */}
            <FastImageViewer
              src={imageUrl}
              originalSrc={originalImageUrl}
              alt={`${t('ImageGenerator.generatedImage')} ${index + 1}`}
              className="rounded-lg"
              small={false}
              showOriginal={showingOriginal === index}
            />

            {/* 按钮组 - 一直显示，不需要hover */}
            <div className="absolute top-2 right-2 flex gap-2 z-10">
              {/* 按住显示原图按钮 */}
              {canShowComparison && (
                <button
                  onMouseDown={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShowOriginal(index, true)
                  }}
                  onTouchStart={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShowOriginal(index, true)
                  }}
                  onTouchEnd={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShowOriginal(index, false)
                  }}
                  onTouchCancel={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShowOriginal(index, false)
                  }}
                  className="p-2 rounded-full bg-black/70 text-white hover:bg-black/90 transition-colors select-none cursor-pointer"
                  title={t('GeneratedImageGrid.showOriginal', {
                    fallback: 'Hold to show original',
                  })}
                  type="button"
                >
                  <Eye className="h-4 w-4" />
                </button>
              )}

              {/* 下载按钮 */}
              <button
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  onDownload(imageUrl, index)
                }}
                className="p-2 rounded-full bg-black/70 text-white hover:bg-black/90 transition-colors cursor-pointer"
                title={t('ImageGenerator.download')}
                type="button"
              >
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* 下载所有图片按钮 - 多张图片时显示 */}
      {imageUrls.length > 1 && (
        <div className="w-full flex justify-center mt-4">
          <button
            onClick={onDownloadAll}
            className="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors border border-gray-300"
          >
            <Download className="mr-2 h-4 w-4" />
            Download All Images
          </button>
        </div>
      )}
    </div>
  )
}
