'use client'

import { useEffect, useState, useCallback } from 'react'
import { imageCache } from '../lib/imageCache'

interface UseImagePreloadOptions {
  /** 是否立即开始预加载 */
  immediate?: boolean
  /** 预加载失败时的回调 */
  onError?: (error: Error) => void
  /** 预加载成功时的回调 */
  onLoad?: (image: HTMLImageElement) => void
}

interface UseImagePreloadReturn {
  /** 图片是否已预加载完成 */
  isLoaded: boolean
  /** 是否正在加载 */
  isLoading: boolean
  /** 预加载错误 */
  error: Error | null
  /** 手动触发预加载 */
  preload: () => Promise<HTMLImageElement | null>
  /** 获取缓存的图片 */
  getCachedImage: () => HTMLImageElement | null
}

/**
 * 图片预加载 Hook
 * @param url 图片URL
 * @param options 选项
 * @returns 预加载状态和控制函数
 */
export function useImagePreload(
  url: string | null | undefined,
  options: UseImagePreloadOptions = {}
): UseImagePreloadReturn {
  const { immediate = true, onError, onLoad } = options

  const [isLoaded, setIsLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  // 检查图片是否已缓存
  useEffect(() => {
    if (url && imageCache.has(url)) {
      setIsLoaded(true)
      setIsLoading(false)
      setError(null)
    }
  }, [url])

  // 预加载函数
  const preload = useCallback(async (): Promise<HTMLImageElement | null> => {
    if (!url) return null

    // 如果已经缓存，直接返回
    if (imageCache.has(url)) {
      const cachedImage = imageCache.get(url)
      if (cachedImage) {
        setIsLoaded(true)
        setIsLoading(false)
        setError(null)
        onLoad?.(cachedImage)
        return cachedImage
      }
    }

    setIsLoading(true)
    setError(null)

    try {
      const image = await imageCache.preload(url)
      setIsLoaded(true)
      setIsLoading(false)
      onLoad?.(image)
      return image
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to preload image')
      setError(error)
      setIsLoading(false)
      setIsLoaded(false)
      onError?.(error)
      return null
    }
  }, [url, onError, onLoad])

  // 获取缓存的图片
  const getCachedImage = useCallback((): HTMLImageElement | null => {
    if (!url) return null
    return imageCache.get(url)
  }, [url])

  // 自动预加载
  useEffect(() => {
    if (immediate && url && !isLoaded && !isLoading) {
      preload()
    }
  }, [immediate, url, isLoaded, isLoading, preload])

  return {
    isLoaded,
    isLoading,
    error,
    preload,
    getCachedImage,
  }
}

/**
 * 批量图片预加载 Hook
 * @param urls 图片URL数组
 * @param options 选项
 * @returns 批量预加载状态和控制函数
 */
export function useBatchImagePreload(
  urls: (string | null | undefined)[],
  options: UseImagePreloadOptions = {}
) {
  const { immediate = true, onError, onLoad } = options

  const [loadedCount, setLoadedCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Error[]>([])

  const validUrls = urls.filter((url): url is string => Boolean(url))
  const totalCount = validUrls.length

  // 批量预加载函数
  const preloadAll = useCallback(async () => {
    if (validUrls.length === 0) return

    setIsLoading(true)
    setErrors([])
    setLoadedCount(0)

    const results = await Promise.allSettled(
      validUrls.map((url) => imageCache.preload(url))
    )

    let successCount = 0
    const newErrors: Error[] = []

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successCount++
        onLoad?.(result.value)
      } else {
        const error = new Error(`Failed to preload image: ${validUrls[index]}`)
        newErrors.push(error)
        onError?.(error)
      }
    })

    setLoadedCount(successCount)
    setErrors(newErrors)
    setIsLoading(false)
  }, [validUrls, onError, onLoad])

  // 自动预加载
  useEffect(() => {
    if (immediate && validUrls.length > 0) {
      preloadAll()
    }
  }, [immediate, validUrls.length, preloadAll])

  return {
    loadedCount,
    totalCount,
    isLoading,
    errors,
    isAllLoaded: loadedCount === totalCount && totalCount > 0,
    preloadAll,
  }
}

/**
 * 智能图片预加载 Hook
 * 根据用户交互自动预加载相关图片
 */
export function useSmartImagePreload() {
  const [preloadQueue, setPreloadQueue] = useState<string[]>([])

  // 添加到预加载队列
  const addToQueue = useCallback((urls: string | string[]) => {
    const urlArray = Array.isArray(urls) ? urls : [urls]
    setPreloadQueue((prev) => {
      const newUrls = urlArray.filter((url) => !prev.includes(url))
      return [...prev, ...newUrls]
    })
  }, [])

  // 处理预加载队列
  useEffect(() => {
    if (preloadQueue.length > 0) {
      // 批量预加载，但不阻塞UI
      const preloadBatch = async () => {
        const batch = preloadQueue.slice(0, 3) // 每次处理3个
        await Promise.allSettled(batch.map((url) => imageCache.preload(url)))
        setPreloadQueue((prev) => prev.slice(3))
      }

      const timeoutId = setTimeout(preloadBatch, 100)
      return () => clearTimeout(timeoutId)
    }
  }, [preloadQueue])

  return {
    addToQueue,
    queueLength: preloadQueue.length,
  }
}
