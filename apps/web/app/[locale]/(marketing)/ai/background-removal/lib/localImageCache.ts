/**
 * 本地图片缓存管理器
 * 使用 Blob URL 和 sessionStorage 来缓存图片，提高查看原图的响应速度
 */

interface CachedImageData {
  blobUrl: string
  timestamp: number
  originalUrl: string
}

class LocalImageCache {
  private cache = new Map<string, CachedImageData>()
  private maxCacheSize = 20 // 最大缓存数量
  private maxAge = 30 * 60 * 1000 // 30分钟过期时间

  /**
   * 缓存图片到本地 Blob URL
   * @param url 原始图片URL
   * @returns Promise<string> 返回本地 Blob URL
   */
  async cacheImage(url: string): Promise<string> {
    // 检查是否已缓存且未过期
    const cached = this.cache.get(url)
    if (cached && !this.isExpired(cached)) {
      return cached.blobUrl
    }

    try {
      // 获取图片数据
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`)
      }

      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)

      // 缓存数据
      const cacheData: CachedImageData = {
        blobUrl,
        timestamp: Date.now(),
        originalUrl: url
      }

      this.cache.set(url, cacheData)
      this.cleanupCache()

      return blobUrl
    } catch (error) {
      console.error('Failed to cache image:', error)
      // 如果缓存失败，返回原始URL
      return url
    }
  }

  /**
   * 获取缓存的图片URL
   * @param url 原始图片URL
   * @returns string | null 返回缓存的 Blob URL 或 null
   */
  getCachedUrl(url: string): string | null {
    const cached = this.cache.get(url)
    if (cached && !this.isExpired(cached)) {
      return cached.blobUrl
    }
    return null
  }

  /**
   * 检查图片是否已缓存
   * @param url 原始图片URL
   * @returns boolean
   */
  isCached(url: string): boolean {
    const cached = this.cache.get(url)
    return cached !== undefined && !this.isExpired(cached)
  }

  /**
   * 预加载图片（不阻塞UI）
   * @param url 图片URL
   * @returns Promise<void>
   */
  async preload(url: string): Promise<void> {
    if (this.isCached(url)) {
      return
    }

    // 在后台预加载
    this.cacheImage(url).catch(error => {
      console.warn('Failed to preload image:', url, error)
    })
  }

  /**
   * 批量预加载图片
   * @param urls 图片URL数组
   * @returns Promise<void>
   */
  async preloadBatch(urls: string[]): Promise<void> {
    const uncachedUrls = urls.filter(url => !this.isCached(url))
    
    // 分批处理，避免同时发起太多请求
    const batchSize = 3
    for (let i = 0; i < uncachedUrls.length; i += batchSize) {
      const batch = uncachedUrls.slice(i, i + batchSize)
      await Promise.allSettled(
        batch.map(url => this.cacheImage(url))
      )
      
      // 添加小延迟避免过度占用网络
      if (i + batchSize < uncachedUrls.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanupCache() {
    const now = Date.now()
    const entries = Array.from(this.cache.entries())

    // 移除过期项
    entries.forEach(([url, data]) => {
      if (this.isExpired(data)) {
        URL.revokeObjectURL(data.blobUrl)
        this.cache.delete(url)
      }
    })

    // 如果缓存仍然过大，移除最旧的项
    if (this.cache.size > this.maxCacheSize) {
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)

      const toRemove = sortedEntries.slice(0, this.cache.size - this.maxCacheSize)
      toRemove.forEach(([url, data]) => {
        URL.revokeObjectURL(data.blobUrl)
        this.cache.delete(url)
      })
    }
  }

  /**
   * 检查缓存项是否过期
   * @param data 缓存数据
   * @returns boolean
   */
  private isExpired(data: CachedImageData): boolean {
    return Date.now() - data.timestamp > this.maxAge
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.forEach(data => {
      URL.revokeObjectURL(data.blobUrl)
    })
    this.cache.clear()
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      items: Array.from(this.cache.entries()).map(([url, data]) => ({
        originalUrl: url,
        blobUrl: data.blobUrl,
        age: Date.now() - data.timestamp,
        expired: this.isExpired(data)
      }))
    }
  }
}

// 创建全局缓存实例
export const localImageCache = new LocalImageCache()

/**
 * 获取优化的图片URL（优先使用缓存）
 * @param url 原始图片URL
 * @returns string 优化后的图片URL
 */
export const getOptimizedImageUrl = (url: string): string => {
  const cachedUrl = localImageCache.getCachedUrl(url)
  return cachedUrl || url
}

/**
 * 预加载图片的便捷函数
 * @param url 图片URL
 * @returns Promise<void>
 */
export const preloadImageToCache = (url: string) => localImageCache.preload(url)

/**
 * 批量预加载图片的便捷函数
 * @param urls 图片URL数组
 * @returns Promise<void>
 */
export const preloadImagesToCache = (urls: string[]) => localImageCache.preloadBatch(urls)

// 页面卸载时清理缓存
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    localImageCache.clear()
  })
}
