import { atom } from 'jotai'
import { z } from 'zod'

// 定义表单验证模式
export const formSchema = z.object({
  // scale: z.enum(['2', '4', '8']).default('2'),
})

export type FormData = z.infer<typeof formSchema>

// 表单数据状态
export const formDataAtom = atom<FormData | null>(null)

// 定义整合的图片数据结构
export interface ImageItem {
  // 本地文件对象（仅在当前会话有效）
  file?: File
  // 本地预览URL（仅在当前会话有效）
  previewUrl: string
  // OSS URL（持久化存储）
  ossUrl?: string
  // 上传状态
  uploading: boolean
  // 上传错误信息
  error?: string
}

// 整合的图片数据数组
export const imagesAtom = atom<ImageItem[]>([])

// OSS URL数组
export const persistedOssUrlsAtom = atom<string[]>([])

export const generatedTaskIdAtom = atom<string | null>(null)

// 图片生成相关状态
export const generationProgressAtom = atom<number>(0)
export const isGeneratingAtom = atom<boolean>(false)
export const generationErrorAtom = atom<string | null>(null)
export const generatedImageUrlAtom = atom<string | null>(null)
export const generatedImageUrlsAtom = atom<string[]>([])

// 原始图片URL（用于前后对比）
export const originalImageUrlAtom = atom<string | null>(null)

// 当前任务ID的全局状态
export const currentTaskIdAtom = atom<string | null>(null)

// 派生原子 - 用于重置所有状态
export const resetAtom = atom(
  null, // 读取时返回null
  (_get, set) => {
    set(imagesAtom, [])
    set(persistedOssUrlsAtom, [])
    set(formDataAtom, null)
    set(generatedTaskIdAtom, null)
    set(generationProgressAtom, 0)
    set(isGeneratingAtom, false)
    set(generationErrorAtom, null)
    set(generatedImageUrlAtom, null)
    set(generatedImageUrlsAtom, [])
    set(originalImageUrlAtom, null)
    set(currentTaskIdAtom, null)
  }
)
