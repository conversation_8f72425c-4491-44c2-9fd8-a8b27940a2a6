/**
 * 图片缓存管理器
 * 用于预加载和缓存图片，提高查看原图的响应速度
 */

interface CacheItem {
  image: HTMLImageElement
  timestamp: number
  loading: boolean
}

class ImageCache {
  private cache = new Map<string, CacheItem>()
  private maxCacheSize = 50 // 最大缓存数量
  private maxAge = 30 * 60 * 1000 // 30分钟过期时间

  /**
   * 预加载图片
   * @param url 图片URL
   * @returns Promise<HTMLImageElement>
   */
  async preload(url: string): Promise<HTMLImageElement> {
    // 检查缓存
    const cached = this.cache.get(url)
    if (cached && !cached.loading) {
      // 更新时间戳
      cached.timestamp = Date.now()
      return cached.image
    }

    // 如果正在加载，等待加载完成
    if (cached && cached.loading) {
      return new Promise((resolve, reject) => {
        const checkLoading = () => {
          const item = this.cache.get(url)
          if (item && !item.loading) {
            resolve(item.image)
          } else {
            setTimeout(checkLoading, 50)
          }
        }
        checkLoading()
      })
    }

    // 创建新的缓存项
    const image = new Image()
    const cacheItem: CacheItem = {
      image,
      timestamp: Date.now(),
      loading: true
    }
    
    this.cache.set(url, cacheItem)

    return new Promise((resolve, reject) => {
      image.onload = () => {
        cacheItem.loading = false
        cacheItem.timestamp = Date.now()
        this.cleanupCache()
        resolve(image)
      }

      image.onerror = () => {
        this.cache.delete(url)
        reject(new Error(`Failed to load image: ${url}`))
      }

      // 设置高优先级加载
      image.loading = 'eager'
      image.src = url
    })
  }

  /**
   * 获取缓存的图片
   * @param url 图片URL
   * @returns HTMLImageElement | null
   */
  get(url: string): HTMLImageElement | null {
    const cached = this.cache.get(url)
    if (cached && !cached.loading && !this.isExpired(cached)) {
      // 更新时间戳
      cached.timestamp = Date.now()
      return cached.image
    }
    return null
  }

  /**
   * 检查图片是否已缓存
   * @param url 图片URL
   * @returns boolean
   */
  has(url: string): boolean {
    const cached = this.cache.get(url)
    return cached !== undefined && !cached.loading && !this.isExpired(cached)
  }

  /**
   * 批量预加载图片
   * @param urls 图片URL数组
   * @returns Promise<HTMLImageElement[]>
   */
  async preloadBatch(urls: string[]): Promise<HTMLImageElement[]> {
    const promises = urls.map(url => this.preload(url).catch(err => {
      console.warn(`Failed to preload image: ${url}`, err)
      return null
    }))
    
    const results = await Promise.all(promises)
    return results.filter(img => img !== null) as HTMLImageElement[]
  }

  /**
   * 清理过期缓存
   */
  private cleanupCache() {
    const now = Date.now()
    const entries = Array.from(this.cache.entries())

    // 移除过期项
    entries.forEach(([url, item]) => {
      if (this.isExpired(item)) {
        this.cache.delete(url)
      }
    })

    // 如果缓存仍然过大，移除最旧的项
    if (this.cache.size > this.maxCacheSize) {
      const sortedEntries = entries
        .filter(([, item]) => !this.isExpired(item))
        .sort((a, b) => a[1].timestamp - b[1].timestamp)

      const toRemove = sortedEntries.slice(0, this.cache.size - this.maxCacheSize)
      toRemove.forEach(([url]) => {
        this.cache.delete(url)
      })
    }
  }

  /**
   * 检查缓存项是否过期
   * @param item 缓存项
   * @returns boolean
   */
  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.timestamp > this.maxAge
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear()
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      items: Array.from(this.cache.entries()).map(([url, item]) => ({
        url,
        loading: item.loading,
        age: Date.now() - item.timestamp
      }))
    }
  }
}

// 创建全局缓存实例
export const imageCache = new ImageCache()

/**
 * 预加载图片的便捷函数
 * @param url 图片URL
 * @returns Promise<HTMLImageElement>
 */
export const preloadImage = (url: string) => imageCache.preload(url)

/**
 * 批量预加载图片的便捷函数
 * @param urls 图片URL数组
 * @returns Promise<HTMLImageElement[]>
 */
export const preloadImages = (urls: string[]) => imageCache.preloadBatch(urls)

/**
 * 检查图片是否已缓存的便捷函数
 * @param url 图片URL
 * @returns boolean
 */
export const isImageCached = (url: string) => imageCache.has(url)
