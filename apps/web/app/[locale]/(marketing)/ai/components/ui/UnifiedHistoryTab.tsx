'use client'

import React, { useEffect, useState, useRef, useCallback } from 'react'
import { Button } from '@ui/components/button'
import { Card, CardContent } from '@ui/components/card'
import { Badge } from '@ui/components/badge'
import { Skeleton } from '@ui/components/skeleton'
import {
  RefreshCw,
  Download,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  ImageIcon,
  VideoIcon,
  Loader2,
  LogIn,
  HistoryIcon,
} from 'lucide-react'
import { TaskType, UnifiedHistoryItem } from '../types'
import { getTaskConfig } from '../config'
import { useUnifiedHistory } from '../hooks'
import { UnifiedTaskDetailModal } from './UnifiedTaskDetailModal'
import { downloadFile } from '../utils'
import { useAuth } from '@ui/hooks/use-auth'
import { useModal } from '@shared/hooks/useModal'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'

interface UnifiedHistoryTabProps {
  taskType: TaskType
  onRegenerate?: (input: Record<string, any>) => void
}

interface HistoryItemCardProps {
  item: UnifiedHistoryItem
  taskType: TaskType
  onViewDetails: (item: UnifiedHistoryItem) => void
  onDownload: (url: string, filename: string) => void
  onPreviewClick: (url: string, isVideo: boolean) => void
}

// 历史记录项卡片组件
const HistoryItemCard: React.FC<HistoryItemCardProps> = ({
  item,
  taskType,
  onViewDetails,
  onDownload,
  onPreviewClick,
}) => {
  const config = getTaskConfig(taskType)
  const fieldMapping = config.fieldMapping
  const [isDownloading, setIsDownloading] = useState(false)

  // 获取结果媒体URL
  const getResultUrl = (): string | null => {
    if (taskType === 'photo-to-anime') {
      console.log('item.resultData', item.resultData)
      return item.resultData?.imageUrls?.[0] || null
    }

    return (
      item.resultData?.[fieldMapping.output.imageUrl || 'imageUrl'] ||
      item.resultData?.[fieldMapping.output.videoUrl || 'videoUrl'] ||
      null
    )
  }

  // 获取预览图URL
  const getPreviewUrl = (): string | null => {
    const resultUrl = getResultUrl()
    if (resultUrl) return resultUrl

    // 如果没有结果，使用输入图片作为预览
    return (
      item.inputParams?.[fieldMapping.input.firstImage || 'imageUrl'] ||
      item.inputParams?.[fieldMapping.input.imageUrl || 'imageUrl'] ||
      null
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handleDownload = async (url: string, filename: string) => {
    try {
      setIsDownloading(true)
      await onDownload(url, filename)
    } catch (error) {
      console.error('Download failed:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  const statusConfig = {
    SUCCESS: {
      label: 'Completed',
      badgeStatus: 'success' as const,
      icon: CheckCircle,
      color: 'text-green-600',
    },
    FAILED: {
      label: 'Failed',
      badgeStatus: 'error' as const,
      icon: XCircle,
      color: 'text-red-600',
    },
    PROCESSING: {
      label: 'Processing',
      badgeStatus: 'info' as const,
      icon: Clock,
      color: 'text-blue-600',
    },
    PENDING: {
      label: 'Pending',
      badgeStatus: 'warning' as const,
      icon: AlertCircle,
      color: 'text-yellow-600',
    },
  }

  const status = statusConfig[item.status]
  const previewUrl = getPreviewUrl()
  const resultUrl = getResultUrl()
  const StatusIcon = status.icon
  const isVideo = config.mediaType === 'video'

  // 处理预览区域点击
  const handlePreviewAreaClick = () => {
    if (previewUrl) {
      onPreviewClick(previewUrl, isVideo && item.status === 'SUCCESS')
    }
  }

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
      <CardContent className="p-0">
        {/* 预览区域 */}
        <div
          className="relative aspect-video bg-gray-100 cursor-pointer"
          onClick={handlePreviewAreaClick}
        >
          {previewUrl ? (
            <>
              {isVideo && item.status === 'SUCCESS' ? (
                <video
                  src={previewUrl}
                  className="w-full h-full object-contain"
                  muted
                >
                  <img
                    src={previewUrl}
                    alt="Video thumbnail"
                    className="w-full h-full object-contain"
                  />
                </video>
              ) : (
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-full object-contain"
                />
              )}
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <div className="text-center">
                {isVideo ? (
                  <VideoIcon className="h-8 w-8 text-gray-400 mx-auto mb-1" />
                ) : (
                  <ImageIcon className="h-8 w-8 text-gray-400 mx-auto mb-1" />
                )}
                <p className="text-xs text-gray-500">No preview</p>
              </div>
            </div>
          )}
        </div>

        {/* 内容区域 */}
        <div className="p-4 space-y-3">
          {/* 任务信息 */}
          <div className="space-y-1 flex justify-between items-center">
            <p className="text-xs text-gray-500 font-mono py-1">
              ID: {item.taskId.slice(-8)}
            </p>

            {/* 状态徽章 */}

            <Badge status={status.badgeStatus} className="text-xs flex">
              <StatusIcon className="h-3 w-3 mr-1" />
              {status.label}
            </Badge>
          </div>

          {/* 时间信息 */}
          <div className="flex items-center text-xs text-gray-500">
            <Calendar className="h-3 w-3 mr-1" />
            {formatDate(item.createdAt)}
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => onViewDetails(item)}
            >
              View Details
            </Button>

            {resultUrl && item.status === 'SUCCESS' && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleDownload(resultUrl, `${taskType}_${item.taskId}`)
                }}
                disabled={isDownloading}
              >
                {isDownloading ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <Download className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 加载骨架组件
const HistoryItemSkeleton: React.FC = () => (
  <Card className="overflow-hidden">
    <CardContent className="p-0">
      <Skeleton className="aspect-video w-full" />
      <div className="p-4 space-y-3">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
        <div className="flex gap-2">
          <Skeleton className="h-8 flex-1" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    </CardContent>
  </Card>
)

// 底部加载指示器组件
const LoadingIndicator: React.FC = () => (
  <div className="flex justify-center items-center py-4">
    <Loader2 className="h-6 w-6 text-primary animate-spin mr-2" />
    <span className="text-sm text-gray-500">Loading more...</span>
  </div>
)

export const UnifiedHistoryTab: React.FC<UnifiedHistoryTabProps> = ({
  taskType,
  onRegenerate,
}) => {
  const { isLoggedIn } = useAuth()
  const { showLoginModal } = useModal()

  const {
    items,
    isLoading,
    error,
    hasMore,
    fetchHistory,
    refreshHistory,
    currentPage,
  } = useUnifiedHistory(taskType)

  const [selectedTask, setSelectedTask] = useState<UnifiedHistoryItem | null>(
    null
  )
  const [previewMedia, setPreviewMedia] = useState<{
    url: string
    isVideo: boolean
  } | null>(null)

  // 创建底部观察元素的引用
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadMoreRef = useRef<HTMLDivElement>(null)

  // 添加请求状态跟踪，防止重复请求
  const isLoadingRef = useRef(false)

  // 存储当前页码，避免依赖于 currentPage 状态
  const currentPageRef = useRef(currentPage)

  // 处理预览点击
  const handlePreviewClick = useCallback((url: string, isVideo: boolean) => {
    setPreviewMedia({ url, isVideo })
  }, [])

  // 处理登录按钮点击
  const handleLoginClick = useCallback(() => {
    showLoginModal({
      title: `View Your History`,
      content: `Sign in to view your generation history and manage your creations.`,
      props: {
        needBottomArea: true,
      },
    })
  }, [showLoginModal, taskType])

  // 如果用户未登录，显示登录提示界面
  if (!isLoggedIn) {
    return (
      <div className="space-y-6 h-full flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold capitalize">History</h3>
        </div>

        {/* 未登录提示界面 */}
        <div className="flex-1 flex">
          <div className="text-center max-w-md mx-auto">
            {/* 图标 */}
            <div className="mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <HistoryIcon className="h-10 w-10 text-white" />
              </div>
            </div>

            {/* 标题 */}
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              View Your History
            </h3>

            {/* 描述 */}
            <p className="text-gray-600 mb-8 leading-relaxed">
              Sign in to access your generation history, download your
              creations, and manage your past projects.
            </p>

            {/* 登录按钮 */}
            <Button
              onClick={handleLoginClick}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 mx-auto shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
            >
              <LogIn className="h-5 w-5" />
              <span>Sign In to View History</span>
            </Button>

            {/* 额外信息 */}
            <div className="mt-8 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
              <h4 className="text-sm font-medium text-purple-800 mb-2">
                ✨ What you'll get:
              </h4>
              <ul className="text-sm text-purple-600 space-y-1">
                <li>• Complete generation history</li>
                <li>• Download your creations</li>
                <li>• Track your progress</li>
                <li>• Manage your projects</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 更新 currentPageRef
  useEffect(() => {
    currentPageRef.current = currentPage
  }, [currentPage])

  // 加载更多数据的处理函数
  const handleLoadMore = useCallback(() => {
    // 如果已经在加载中或没有更多数据，则不触发加载
    if (isLoadingRef.current || !hasMore) return

    // 设置加载状态，防止重复请求
    isLoadingRef.current = true

    // 使用 currentPageRef 而不是 currentPage，避免依赖变化
    const nextPage = currentPageRef.current + 1

    console.log(`Loading page ${nextPage}...`) // 调试日志

    fetchHistory(nextPage, 12).finally(() => {
      // 请求完成后，重置加载状态
      isLoadingRef.current = false
    })
  }, [hasMore, fetchHistory]) // 移除 isLoading 和 currentPage 依赖

  // 设置 Intersection Observer
  useEffect(() => {
    // 清理之前的观察者
    if (observerRef.current) {
      observerRef.current.disconnect()
      observerRef.current = null
    }

    // 如果已经没有更多数据，则不需要观察
    if (!hasMore) return

    // 创建一个防抖函数，限制触发频率
    let debounceTimer: NodeJS.Timeout | null = null
    const debouncedLoadMore = () => {
      if (debounceTimer) clearTimeout(debounceTimer)

      debounceTimer = setTimeout(() => {
        if (!isLoadingRef.current) {
          handleLoadMore()
        }
      }, 500) // 500ms 的防抖时间
    }

    const observer = new IntersectionObserver(
      (entries) => {
        // 当观察元素进入视口时
        if (entries[0].isIntersecting) {
          debouncedLoadMore()
        }
      },
      {
        threshold: 0.5, // 当 50% 的元素可见时触发，提高阈值减少误触发
        rootMargin: '0px', // 不提前触发
      }
    )

    observerRef.current = observer

    // 开始观察底部元素
    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    // 清理函数
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
    }
  }, [hasMore, handleLoadMore]) // 移除 isLoading 依赖

  // 初始加载
  useEffect(() => {
    // 只在组件挂载时加载第一页数据
    fetchHistory(1, 12)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []) // 空依赖数组，确保只在挂载时执行一次

  if (error) {
    return (
      <div className="text-center py-8 h-full flex flex-col justify-center items-center">
        <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Error Loading History
        </h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <Button onClick={() => refreshHistory()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold capitalize">History</h3>
        <Button
          onClick={() => {
            // 重置加载状态和页码引用
            isLoadingRef.current = false
            currentPageRef.current = 1
            refreshHistory()
          }}
          variant="outline"
          size="sm"
          disabled={isLoading}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
      </div>

      {/* 历史记录网格 */}
      {items.length === 0 && !isLoading ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            {getTaskConfig(taskType).mediaType === 'video' ? (
              <VideoIcon className="h-16 w-16 mx-auto" />
            ) : (
              <ImageIcon className="h-16 w-16 mx-auto" />
            )}
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No History Yet
          </h3>
          <p className="text-gray-500">
            Your {taskType.replace('_', ' ')} generations will appear here.
          </p>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto min-h-[400px] pr-1">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {items.map((item) => (
              <HistoryItemCard
                key={item.id}
                item={item}
                taskType={taskType}
                onViewDetails={setSelectedTask}
                onDownload={downloadFile}
                onPreviewClick={handlePreviewClick}
              />
            ))}

            {/* 加载骨架 */}
            {isLoading &&
              Array.from({ length: 4 }).map((_, index) => (
                <HistoryItemSkeleton key={`skeleton-${index}`} />
              ))}
          </div>

          {/* 底部观察元素 - 用于触发加载更多 */}
          {hasMore && (
            <div ref={loadMoreRef} className="h-20 w-full mt-4">
              {isLoading && <LoadingIndicator />}
            </div>
          )}
        </div>
      )}

      {/* 任务详情弹窗 */}
      <UnifiedTaskDetailModal
        isOpen={!!selectedTask}
        onClose={() => setSelectedTask(null)}
        task={selectedTask}
        onRegenerate={onRegenerate}
      />

      {/* 媒体预览弹窗 */}
      <Dialog open={!!previewMedia} onOpenChange={() => setPreviewMedia(null)}>
        <DialogContent className="max-w-3xl max-h-[95vh] bg-white">
          <DialogHeader className="px-2">
            <DialogTitle>Preview</DialogTitle>
          </DialogHeader>
          <div className="p-2">
            <div className="relative">
              {previewMedia &&
                (previewMedia.isVideo ? (
                  <video
                    src={previewMedia.url}
                    controls
                    autoPlay
                    className="w-full h-auto max-h-[70vh] object-contain"
                  >
                    Your browser does not support the video tag.
                  </video>
                ) : (
                  <img
                    src={previewMedia.url}
                    alt="Enlarged view"
                    className="w-full h-auto max-h-[70vh] object-contain"
                  />
                ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
