'use client'

import React from 'react'
import { Monitor, Clock } from 'lucide-react'
import { IMAGE_TO_VIDEO_CONFIG } from '../../image-to-video/config'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'

interface VideoSettingsPanelProps {
  quality: string
  duration: number
  onQualityChange: (quality: string) => void
  onDurationChange: (duration: number) => void
  disabled?: boolean
  className?: string
  compatibleQualities?: ReadonlyArray<{
    readonly value: string
    readonly label: string
    readonly description: string
  }>
  compatibleDurations?: ReadonlyArray<{
    readonly value: number
    readonly label: string
    readonly description: string
  }>
}

export function VideoSettingsPanel({
  quality,
  duration,
  onQualityChange,
  onDurationChange,
  disabled = false,
  className = '',
  compatibleQualities,
  compatibleDurations,
}: VideoSettingsPanelProps) {
  const { qualities, durations } = IMAGE_TO_VIDEO_CONFIG.videoSettings

  // 使用所有选项，但根据兼容性设置disabled状态
  const allQualities = qualities
  const allDurations = durations

  // 检查选项是否兼容
  const isQualityCompatible = (qualityValue: string) => {
    if (!compatibleQualities) return true
    return compatibleQualities.some((q) => q.value === qualityValue)
  }

  const isDurationCompatible = (durationValue: number) => {
    if (!compatibleDurations) return true
    return compatibleDurations.some((d) => d.value === durationValue)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-2 gap-4">
        {/* 画质设置 */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Monitor className="w-4 h-4 text-gray-500" />
            <label className="text-md font-semibold text-gray-900">
              Quality
            </label>
          </div>
          <Select
            value={quality}
            onValueChange={onQualityChange}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select quality" />
            </SelectTrigger>
            <SelectContent>
              {allQualities.map((q) => (
                <SelectItem
                  key={q.value}
                  value={q.value}
                  disabled={!isQualityCompatible(q.value)}
                >
                  {q.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 时长设置 */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-gray-500" />
            <label className="text-md font-semibold text-gray-900">
              Duration
            </label>
          </div>
          <Select
            value={duration.toString()}
            onValueChange={(value) => onDurationChange(Number(value))}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select duration" />
            </SelectTrigger>
            <SelectContent>
              {allDurations.map((d) => (
                <SelectItem
                  key={d.value}
                  value={d.value.toString()}
                  disabled={!isDurationCompatible(d.value)}
                >
                  {d.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}
