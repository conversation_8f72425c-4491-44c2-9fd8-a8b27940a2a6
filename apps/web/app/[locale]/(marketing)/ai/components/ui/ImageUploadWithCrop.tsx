'use client'

import React, { useState, useRef, use<PERSON><PERSON>back, useMemo } from 'react'
import { Upload, X, Crop, RotateCcw } from 'lucide-react'
import { Button } from '@ui/components/button'
import { Progress } from '@ui/components/progress'
import { useToast } from '@ui/hooks/use-toast'
import <PERSON>ropper from 'react-cropper'
import type { ReactCropperElement } from 'react-cropper'
import 'cropperjs/dist/cropper.css'

interface ImageUploadWithCropProps {
  imageUrl?: string | null
  isUploading?: boolean
  progress?: number
  onImageChange?: (file: File, croppedBlob: Blob | null) => void
  onRemove?: () => void
  aspectRatio?: string
  className?: string
  disabled?: boolean
  maxFileSize?: number
  allowedTypes?: string[]
  ref?: React.RefObject<{ getCroppedBlob: () => Promise<Blob | null> }>
}

const ASPECT_RATIO_MAP: Record<string, number> = {
  '16:9': 16 / 9,
  '9:16': 9 / 16,
  '1:1': 1,
  '4:3': 4 / 3,
  '3:4': 3 / 4,
  '2:3': 2 / 3,
  '3:2': 3 / 2,
}

// MIME类型到用户友好格式名称的映射
const MIME_TYPE_TO_FORMAT: Record<string, string> = {
  'image/jpeg': 'JPG',
  'image/jpg': 'JPG',
  'image/png': 'PNG',
  'image/webp': 'WEBP',
  'image/gif': 'GIF',
  'image/bmp': 'BMP',
  'image/tiff': 'TIFF',
  'image/svg+xml': 'SVG',
}

// 将MIME类型数组转换为用户友好的格式字符串
const formatMimeTypesToFriendlyString = (allowedTypes: string[]): string => {
  if (!allowedTypes || allowedTypes.length === 0) {
    return 'image files'
  }

  // 转换MIME类型到格式名称并去重
  const uniqueFormats = Array.from(
    new Set(
      allowedTypes
        .map(
          (type) =>
            MIME_TYPE_TO_FORMAT[type] ||
            type.replace('image/', '').toUpperCase()
        )
        .filter(Boolean)
    )
  ).sort()

  if (uniqueFormats.length === 0) {
    return 'image files'
  }

  if (uniqueFormats.length === 1) {
    return `${uniqueFormats[0]} format`
  }

  // 多个格式时用逗号分隔，最后一个用"and"连接
  const lastFormat = uniqueFormats.pop()
  return `${uniqueFormats.join(', ')} and ${lastFormat} formats`
}

export const ImageUploadWithCrop = React.forwardRef<
  { getCroppedBlob: () => Promise<Blob | null> },
  ImageUploadWithCropProps
>(
  (
    {
      imageUrl,
      isUploading = false,
      progress = 0,
      onImageChange,
      onRemove,
      aspectRatio = '16:9',
      className = '',
      disabled = false,
      maxFileSize = 10 * 1024 * 1024, // 10MB
      allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    },
    ref
  ) => {
    const [dragActive, setDragActive] = useState(false)
    const [selectedFile, setSelectedFile] = useState<File | null>(null)
    const [showCropper, setShowCropper] = useState(false)
    const [cropperReady, setCropperReady] = useState(false)
    const cropperRef = useRef<ReactCropperElement>(null)
    const { toast } = useToast()

    const imageSrc = useMemo(() => {
      if (selectedFile) {
        return URL.createObjectURL(selectedFile)
      }
      return imageUrl
    }, [selectedFile, imageUrl])

    const currentAspectRatio = useMemo(() => {
      return ASPECT_RATIO_MAP[aspectRatio] || 16 / 9
    }, [aspectRatio])

    const validateFile = useCallback(
      (file: File): { isValid: boolean; error?: string } => {
        if (!allowedTypes.includes(file.type)) {
          return {
            isValid: false,
            error: `Unsupported file type. Please select images in ${allowedTypes.join(
              ', '
            )} format.`,
          }
        }

        if (file.size > maxFileSize) {
          const maxSizeMB = Math.round(maxFileSize / (1024 * 1024))
          return {
            isValid: false,
            error: `File size cannot exceed ${maxSizeMB}MB`,
          }
        }

        return { isValid: true }
      },
      [allowedTypes, maxFileSize]
    )

    const handleFileSelect = useCallback(
      (file: File) => {
        const validation = validateFile(file)
        if (!validation.isValid) {
          toast({
            title: 'File validation failed',
            description: validation.error,
            variant: 'error',
          })
          return
        }

        setSelectedFile(file)
        setShowCropper(true)
        setCropperReady(false)

        // 通知父组件文件已选择，但不进行裁剪
        if (onImageChange) {
          onImageChange(file, null)
        }
      },
      [validateFile, toast, onImageChange]
    )

    const handleDragEnter = useCallback(
      (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        if (!disabled) {
          setDragActive(true)
        }
      },
      [disabled]
    )

    const handleDragLeave = useCallback((e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setDragActive(false)
    }, [])

    const handleDragOver = useCallback((e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
    }, [])

    const handleDrop = useCallback(
      (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setDragActive(false)

        if (disabled) return

        const files = Array.from(e.dataTransfer.files)
        const file = files[0]

        if (file && file.type.startsWith('image/')) {
          handleFileSelect(file)
        } else {
          toast({
            title: 'Invalid file type',
            description: 'Please select a valid image file',
            variant: 'error',
          })
        }
      },
      [disabled, handleFileSelect, toast]
    )

    const handleClick = useCallback(() => {
      if (disabled) return

      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (file) {
          handleFileSelect(file)
        }
      }
      input.click()
    }, [disabled, handleFileSelect])

    // 获取裁剪后的Blob，供外部调用
    const getCroppedBlob = useCallback(async (): Promise<Blob | null> => {
      if (!cropperRef.current?.cropper) return null

      try {
        const croppedCanvas = cropperRef.current.cropper.getCroppedCanvas()
        const croppedBlob = await new Promise<Blob | null>((resolve) => {
          croppedCanvas.toBlob(resolve, 'image/jpeg', 0.8)
        })
        return croppedBlob
      } catch (error) {
        console.error('Crop failed:', error)
        toast({
          title: 'Crop failed',
          description:
            'An error occurred while cropping the image, please try again',
          variant: 'error',
        })
        return null
      }
    }, [toast])

    // 暴露方法给父组件
    React.useImperativeHandle(
      ref,
      () => ({
        getCroppedBlob,
      }),
      [getCroppedBlob]
    )

    const handleCropperReady = useCallback(() => {
      setCropperReady(true)
    }, [])

    const handleRemove = useCallback(() => {
      setSelectedFile(null)
      setShowCropper(false)
      if (onRemove) {
        onRemove()
      }
    }, [onRemove])

    const handleCancelCrop = useCallback(() => {
      setShowCropper(false)
      setSelectedFile(null)
    }, [])

    // 当aspectRatio改变时更新裁剪框
    React.useEffect(() => {
      if (cropperRef.current?.cropper && cropperReady) {
        cropperRef.current.cropper.setAspectRatio(currentAspectRatio)
      }
    }, [currentAspectRatio, cropperReady])

    if (showCropper && imageSrc) {
      return (
        <div className={`space-y-4 ${className}`}>
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm relative h-[300px]">
            <div className="w-full h-full bg-gray-100 rounded-lg overflow-hidden relative">
              <Cropper
                ref={cropperRef}
                src={imageSrc}
                style={{ height: '300px', width: '100%' }}
                aspectRatio={currentAspectRatio}
                ready={handleCropperReady}
                guides={true}
                viewMode={1}
                dragMode="move"
                zoomable={true}
                zoomOnTouch={true}
                zoomOnWheel={true}
                cropBoxMovable={true}
                cropBoxResizable={true}
                autoCropArea={1}
                checkOrientation={false}
                responsive={true}
                restore={false}
                checkCrossOrigin={false}
                crossOrigin="anonymous"
              />

              {/* 绝对定位的叉号取消按钮 */}
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancelCrop}
                disabled={disabled}
                className="absolute top-2 right-2 z-10 w-8 h-8 p-0 bg-white/90 hover:bg-white text-gray-700 border border-gray-300 rounded-full shadow-sm hover:shadow-md transition-all duration-200"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )
    }

    if (imageSrc && !showCropper) {
      return (
        <div className={`space-y-4 ${className}`}>
          <div className="relative group h-[300px]">
            <div className="relative bg-white rounded-lg border border-gray-200 overflow-hidden h-full">
              <div className="w-full h-full">
                <img
                  src={imageSrc}
                  alt="Uploaded image"
                  className="w-full h-full object-cover"
                />
              </div>

              {isUploading && (
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                  <div className="bg-white rounded-lg p-4 min-w-[200px]">
                    <div className="text-center">
                      <div className="w-8 h-8 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-2">
                        <Upload className="w-4 h-4 text-blue-500 animate-pulse" />
                      </div>
                      <p className="text-sm text-gray-900 mb-2">Uploading...</p>
                      <Progress value={progress} className="h-2" />
                      <p className="text-xs text-gray-600 mt-1">{progress}%</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="absolute top-2 right-2 flex gap-1">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setShowCropper(true)}
                  disabled={disabled || isUploading}
                  className="bg-white/90 hover:bg-white text-gray-700 border border-gray-300"
                >
                  <Crop className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRemove}
                  disabled={disabled || isUploading}
                  className="bg-white/90 hover:bg-red-50 text-red-600 border border-gray-300"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className={`space-y-4 ${className}`}>
        <div
          className={`
          relative rounded-lg border border-dashed text-center cursor-pointer
          transition-all duration-200 h-[300px] flex flex-col items-center justify-center
          ${
            dragActive
              ? 'border-blue-400 bg-blue-50/50 shadow-md'
              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <div className="flex flex-col items-center justify-center space-y-3">
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center ${
                dragActive
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-400'
              }`}
            >
              <Upload className="w-6 h-6" />
            </div>

            <div className="text-center">
              <p className="text-lg font-medium text-gray-900 mb-1">
                {dragActive
                  ? 'Release to upload image'
                  : 'Click or drag to upload image'}
              </p>
              <p className="text-sm text-gray-500">
                Supports {formatMimeTypesToFriendlyString(allowedTypes)}, max{' '}
                {Math.round(maxFileSize / (1024 * 1024))}MB
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }
)

ImageUploadWithCrop.displayName = 'ImageUploadWithCrop'
