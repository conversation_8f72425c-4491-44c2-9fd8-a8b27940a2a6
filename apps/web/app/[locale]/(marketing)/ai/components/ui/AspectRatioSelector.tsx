'use client'

import React from 'react'
import { Button } from '@ui/components/button'
import { IMAGE_TO_VIDEO_CONFIG } from '../../image-to-video/config'

interface AspectRatioSelectorProps {
  selectedRatio: string
  onRatioChange: (ratio: string) => void
  disabled?: boolean
  className?: string
}

const getRatioDisplay = (
  ratio: string
): { icon: string; label: string; description: string } => {
  const config = IMAGE_TO_VIDEO_CONFIG.videoSettings.aspectRatios.find(
    (r) => r.value === ratio
  )
  if (config) {
    return {
      icon: config.icon,
      label: config.label,
      description: config.description,
    }
  }
  return {
    icon: '📺',
    label: ratio,
    description: '',
  }
}

export function AspectRatioSelector({
  selectedRatio,
  onRatioChange,
  disabled = false,
  className = '',
}: AspectRatioSelectorProps) {
  const aspectRatios = IMAGE_TO_VIDEO_CONFIG.videoSettings.aspectRatios

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-md font-semibold text-gray-900">Aspect Ratio</h3>

      <div className="grid grid-cols-2 gap-2">
        {aspectRatios.map((ratio) => {
          const { label } = getRatioDisplay(ratio.value)
          const isSelected = selectedRatio === ratio.value

          return (
            <Button
              key={ratio.value}
              variant={'outline'}
              className={`
                h-auto p-1 flex flex-col items-center justify-center
                transition-all duration-200
                ${
                  isSelected
                    ? 'bg-[#4B6BFB] text-white hover:bg-[#4B6BFB]/80'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={() => !disabled && onRatioChange(ratio.value)}
              disabled={disabled}
            >
              <div className="flex items-center space-x-2">
                {/* <span className="text-lg">{icon}</span> */}
                <span className="font-medium">{label}</span>
              </div>
              {/* <span className="text-xs opacity-75">{description}</span> */}
            </Button>
          )
        })}
      </div>
    </div>
  )
}
