import React from 'react'

export interface GenerationProgressProps {
  /** 当前进度值 (0-100) */
  progress: number
  /** 是否正在生成中 */
  isGenerating?: boolean
  /** 自定义标题 */
  title?: string
  /** 自定义描述 */
  description?: string
  /** 自定义类名 */
  className?: string
  /** 进度条大小 */
  size?: 'sm' | 'md' | 'lg'
}

/**
 * 通用生成进度显示组件
 */
export function GenerationProgress({
  progress,
  isGenerating = true,
  title = 'Generating',
  description = 'AI is processing your request...',
  className = '',
  size = 'md',
}: GenerationProgressProps) {
  const sizeClasses = {
    sm: {
      container: 'p-6',
      circle: 'w-16 h-16',
      progressBar: 'h-2',
      title: 'text-base',
      description: 'text-sm',
      percentage: 'text-lg',
    },
    md: {
      container: 'p-8',
      circle: 'w-20 h-20',
      progressBar: 'h-3',
      title: 'text-lg',
      description: 'text-sm',
      percentage: 'text-2xl',
    },
    lg: {
      container: 'p-10',
      circle: 'w-24 h-24',
      progressBar: 'h-4',
      title: 'text-xl',
      description: 'text-base',
      percentage: 'text-3xl',
    },
  }

  const classes = sizeClasses[size]

  if (!isGenerating) {
    return null
  }

  return (
    <div
      className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 ${classes.container} ${className}`}
    >
      <div className="text-center space-y-8">
        {/* 圆形进度指示器 */}
        <div className={`relative mx-auto ${classes.circle}`}>
          {/* 涟漪效果层 */}
          {isGenerating && (
            <>
              <div
                className={`absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-30 ripple-layer-1`}
              />
              <div
                className={`absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-30 ripple-layer-2`}
              />
              <div
                className={`absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-30 ripple-layer-3`}
              />
            </>
          )}

          {/* 主圆形指示器 */}
          <div
            className={`absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center z-10`}
          >
            <span className={`${classes.percentage} font-bold text-white`}>
              {Math.round(progress)}%
            </span>
          </div>
        </div>

        {/* 进度条 */}
        <div className="w-full max-w-md mx-auto">
          <div
            className={`bg-gray-200 rounded-full ${classes.progressBar} overflow-hidden`}
          >
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-full rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* 文本信息 */}
        <div className="space-y-2">
          <h3 className={`${classes.title} font-semibold text-gray-900`}>
            {progress >= 100 ? 'Processing Final Details...' : title}
          </h3>
          <p className={`${classes.description} text-gray-600`}>
            {progress >= 100 ? 'Almost done! Finalizing...' : description}
          </p>
        </div>
      </div>

      <style jsx>{`
        /* 涟漪效果动画 */
        @keyframes ripple {
          0% {
            transform: scale(1);
            opacity: 0.3;
          }
          100% {
            transform: scale(2);
            opacity: 0;
          }
        }

        .ripple-layer-1 {
          animation: ripple 2.5s ease-out infinite;
        }

        .ripple-layer-2 {
          animation: ripple 3s ease-out infinite;
          animation-delay: 0.5s;
        }

        .ripple-layer-3 {
          animation: ripple 3.5s ease-out infinite;
          animation-delay: 1s;
        }
      `}</style>
    </div>
  )
}
