import toast from 'react-hot-toast'

/**
 * 下载单个文件
 * @param url - 文件URL
 * @param filename - 文件名
 * @returns Promise<void>
 */
export const downloadFile = async (
  url: string,
  filename = 'download'
): Promise<void> => {
  if (!url) return

  const timestamp = Date.now()
  const fileExtension = url.split('.').pop() || 'jpg'
  filename = `${filename}-${timestamp}.${fileExtension}`

  try {
    if (url.includes('mp4')) {
      const response = await fetch(url, { method: 'GET', mode: 'cors' })
      const blob = await response.blob()
      const blobUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename
      link.click()
      window.URL.revokeObjectURL(blobUrl)
      return
    }

    // 首先尝试通过代理API下载
    const response = await fetch(
      `/api/download?url=${encodeURIComponent(
        url
      )}&filename=${encodeURIComponent(filename)}`,
      { method: 'GET' }
    )

    if (!response.ok) {
      // 如果代理下载失败，尝试直接下载
      console.log('Proxy download failed, trying direct download...')
      const blob = await fetch(url, { mode: 'cors' }).then((res) => {
        if (!res.ok) throw new Error(`Failed to fetch: ${res.statusText}`)
        return res.blob()
      })

      // Create a download link for the blob
      const blobUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename

      // Trigger download
      document.body.appendChild(link)
      link.click()

      // Clean up
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
      return
    }

    // 处理代理下载成功的情况
    const blob = await response.blob()
    const blobUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = filename

    // Trigger download
    document.body.appendChild(link)
    link.click()

    // Clean up
    document.body.removeChild(link)
    window.URL.revokeObjectURL(blobUrl)
  } catch (error) {
    console.error('Error downloading file:', error)
    toast.error('下载文件失败，请稍后重试。')
    throw error
  }
}
