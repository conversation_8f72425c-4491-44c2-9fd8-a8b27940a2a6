'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { TaskType, UnifiedTaskDetail, GenerationRequest } from '../types'
import { getTaskConfig } from '../config'
import { AdapterFactory } from '../adapters'
import { useModal } from '@shared/hooks/useModal'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations } from 'next-intl'

// Hook 返回类型
interface UseUnifiedGenerationReturn {
  isGenerating: boolean
  taskId: string | null
  error: string | null
  taskDetail: UnifiedTaskDetail | null
  isPolling: boolean
  pollingError: string | null
  generate: (input: Record<string, any>) => Promise<void>
  reset: () => void
  stopPolling: () => void
}

export function useUnifiedGeneration(
  taskType: TaskType
): UseUnifiedGenerationReturn {
  const [isGenerating, setIsGenerating] = useState(false)
  const [taskId, setTaskId] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [taskDetail, setTaskDetail] = useState<UnifiedTaskDetail | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const [pollingError, setPollingError] = useState<string | null>(null)

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const retryCountRef = useRef<number>(0)
  // 用于管理当前进行中的轮询请求
  const currentPollingRequestRef = useRef<Promise<void> | null>(null)
  // 用于取消请求的控制器
  const abortControllerRef = useRef<AbortController | null>(null)

  const config = getTaskConfig(taskType)
  const adapter = AdapterFactory.getAdapter(taskType)
  const t = useTranslations()
  const { showLoginModal, showInsufficientCreditsModal } = useModal()
  const user = getUserFromClientCookies()

  // 停止轮询
  const stopPolling = useCallback(() => {
    // 清理定时器
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current)
      pollingTimeoutRef.current = null
    }

    // 取消正在进行的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    // 清理请求引用
    currentPollingRequestRef.current = null

    setIsPolling(false)
    retryCountRef.current = 0
  }, [])

  // 轮询任务状态
  const pollTaskStatus = useCallback(
    async (taskId: string) => {
      // 如果有正在进行的请求，等待其完成或取消
      if (currentPollingRequestRef.current) {
        // 取消当前请求
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
        }

        // 等待当前请求完成（可能因为取消而完成）
        try {
          await currentPollingRequestRef.current
        } catch (error) {
          // 忽略取消导致的错误
          if (error instanceof Error && error.name !== 'AbortError') {
            console.warn('Previous polling request error:', error)
          }
        }
      }

      // 创建新的请求控制器
      abortControllerRef.current = new AbortController()

      // 创建新的请求Promise
      const pollingRequest = async () => {
        try {
          const taskDetail = await adapter.getTaskStatus(
            taskId,
            abortControllerRef.current?.signal,
            taskType
          )

          // 检查是否已被取消
          if (abortControllerRef.current?.signal.aborted) {
            return
          }

          setTaskDetail(taskDetail)

          // 重置错误状态
          setPollingError(null)
          retryCountRef.current = 0

          // 检查任务是否完成
          if (
            taskDetail.status.status === 'success' ||
            taskDetail.status.status === 'failed'
          ) {
            stopPolling()
            setIsGenerating(false)

            if (taskDetail.status.status === 'failed') {
              setPollingError(
                taskDetail.error?.message ||
                  taskDetail.status.message ||
                  'Generation failed'
              )
            }
          }
        } catch (error) {
          // 如果是取消错误，不进行错误处理
          if (error instanceof Error && error.name === 'AbortError') {
            return
          }

          console.error('Polling error:', error)
          retryCountRef.current++

          // 如果重试次数超过限制，停止轮询
          if (
            config.pollingConfig.maxRetries &&
            retryCountRef.current >= config.pollingConfig.maxRetries
          ) {
            setPollingError(
              error instanceof Error
                ? `Failed to get task status after ${config.pollingConfig.maxRetries} retries: ${error.message}`
                : `Failed to get task status after ${config.pollingConfig.maxRetries} retries`
            )
            stopPolling()
            setIsGenerating(false)
          } else {
            // 继续重试，但不显示错误（避免频繁错误提示）
            console.warn(
              `Polling retry ${retryCountRef.current}/${config.pollingConfig.maxRetries}`
            )
          }
        } finally {
          // 请求完成后，清理当前请求引用
          currentPollingRequestRef.current = null
        }
      }

      // 保存当前请求的引用
      currentPollingRequestRef.current = pollingRequest()

      // 执行请求
      await currentPollingRequestRef.current
    },
    [adapter, config.pollingConfig.maxRetries, stopPolling]
  )

  // 开始轮询
  const startPolling = useCallback(
    (taskId: string) => {
      // 首先停止之前的轮询，清理所有状态
      stopPolling()

      setIsPolling(true)
      setPollingError(null)
      retryCountRef.current = 0

      // 立即执行一次
      pollTaskStatus(taskId)

      // 设置定期轮询
      pollingIntervalRef.current = setInterval(() => {
        pollTaskStatus(taskId)
      }, config.pollingConfig.interval)

      // 设置轮询超时
      pollingTimeoutRef.current = setTimeout(() => {
        stopPolling()
        setPollingError('Task timeout - generation took too long')
        setIsGenerating(false)
      }, config.pollingConfig.timeout)
    },
    [pollTaskStatus, config.pollingConfig, stopPolling]
  )

  // 生成任务
  const generate = useCallback(
    async (input: Record<string, any>) => {
      try {
        // 检查用户登录状态
        if (!user) {
          showLoginModal({
            title: t('loginTipsTitle'),
            content: t('tipLogin'),
            props: {
              needBottomArea: true, // 显示会员权益
            },
          })
          return
        }

        setIsGenerating(true)
        setError(null)
        setTaskDetail(null)
        setPollingError(null)

        // 合并默认值
        const finalInput = {
          ...config.defaultValues,
          ...input,
        }
        console.log(finalInput)
        const request: GenerationRequest = {
          taskType,
          input: finalInput,
        }

        const response = await adapter.generate(request)

        if (response.success && response.taskId) {
          setTaskId(response.taskId)
          startPolling(response.taskId)
        } else {
          throw new Error(response.error || 'Failed to start generation')
        }
      } catch (error) {
        console.error('Generate error:', error)

        let errorMessage = 'Failed to start generation'

        if (error instanceof Error) {
          errorMessage = error.message
        } else if (typeof error === 'string') {
          errorMessage = error
        }

        // 处理特定错误类型
        if (
          errorMessage.includes('401') ||
          errorMessage.includes('Authentication required')
        ) {
          showLoginModal({
            title: t('loginTipsTitle'),
            content: t('tipLogin'),
            props: {
              needBottomArea: true,
            },
          })
          setIsGenerating(false)
          return
        } else if (
          errorMessage.includes('402') ||
          errorMessage.includes('Insufficient credits')
        ) {
          showInsufficientCreditsModal({
            content: t('ImageStyleConverter.insufficientPoints'),
          })
          setIsGenerating(false)
          return
        }

        setError(errorMessage)
        setIsGenerating(false)
      }
    },
    [
      taskType,
      config.defaultValues,
      adapter,
      startPolling,
      user,
      t,
      showLoginModal,
      showInsufficientCreditsModal,
    ]
  )

  // 重置状态
  const reset = useCallback(() => {
    stopPolling()
    setIsGenerating(false)
    setTaskId(null)
    setError(null)
    setTaskDetail(null)
    setPollingError(null)
  }, [stopPolling])

  // 组件卸载时清理所有请求
  useEffect(() => {
    return () => {
      stopPolling()
    }
  }, [stopPolling])

  return {
    isGenerating,
    taskId,
    error,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset,
    stopPolling,
  }
}
