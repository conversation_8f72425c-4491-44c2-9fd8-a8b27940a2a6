import { useState, useEffect, useCallback, useRef } from 'react'

export interface UseGenerationProgressReturn {
  progress: number
  isActive: boolean
  startProgress: () => void
  stopProgress: () => void
  resetProgress: () => void
  setProgress: (progress: number) => void
}

/**
 * 管理生成进度的Hook
 * @param maxProgress 最大进度值，默认99%
 * @param duration 达到最大进度的时间（毫秒），默认5分钟
 * @returns 进度状态和控制方法
 */
export function useGenerationProgress(
  maxProgress: number = 99,
  duration: number = 300000 // 5分钟
): UseGenerationProgressReturn {
  const [progress, setProgressState] = useState(0)
  const [isActive, setIsActive] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)

  const startProgress = useCallback(() => {
    // 清除现有计时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    setProgressState(0)
    setIsActive(true)
    startTimeRef.current = Date.now()

    // 启动断续式跳跃计时器
    intervalRef.current = setInterval(() => {
      setProgressState((prev) => {
        // 如果已经到达最大进度，停止
        if (prev >= maxProgress) {
          return prev
        }

        // 计算已用时间和剩余时间
        const currentTime = Date.now()
        const elapsedTime = currentTime - (startTimeRef.current || currentTime)
        const remainingTime = duration - elapsedTime
        const remainingProgress = maxProgress - prev

        // 如果时间快用完了，强制跳跃确保完成
        if (remainingTime < duration * 0.1 && remainingProgress > 0) {
          const forceIncrement = Math.min(Math.ceil(remainingProgress / 3), 10)
          return Math.min(prev + forceIncrement, maxProgress)
        }

        // 随机决定是否跳跃（70%概率跳跃，30%概率停顿）
        if (Math.random() < 0.7) {
          // 生成1-10的随机增量
          const randomIncrement = Math.floor(Math.random() * 10) + 1
          const newProgress = Math.min(prev + randomIncrement, maxProgress)
          return newProgress
        }

        // 30%概率保持当前进度（停顿效果）
        return prev
      })
    }, 3000) // 每3000ms检查一次，比原来的200ms慢，增加停顿感
  }, [maxProgress])

  const stopProgress = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsActive(false)
    startTimeRef.current = null
  }, [])

  const resetProgress = useCallback(() => {
    stopProgress()
    setProgressState(0)
    startTimeRef.current = null
  }, [stopProgress])

  const setProgress = useCallback((newProgress: number) => {
    setProgressState(Math.min(Math.max(newProgress, 0), 100))
  }, [])

  // 组件卸载时清理计时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    progress,
    isActive,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress,
  }
}
