'use client'

import { useState, useCallback, useRef } from 'react'
import { TaskType, UnifiedHistoryItem } from '../types'
import { API_ENDPOINTS } from '../config'
import { useAuth } from '@ui/hooks/use-auth'

interface HistoryState {
  items: UnifiedHistoryItem[]
  isLoading: boolean
  error: string | null
  currentPage: number
  totalPages: number
  hasMore: boolean
  taskType: TaskType
}

interface UseUnifiedHistoryReturn extends HistoryState {
  fetchHistory: (page?: number, pageSize?: number) => Promise<void>
  refreshHistory: () => Promise<void>
}

export function useUnifiedHistory(taskType: TaskType): UseUnifiedHistoryReturn {
  const { user, isLoggedIn } = useAuth()

  // 使用 ref 存储加载状态，避免因状态变化导致的函数重新创建
  const isLoadingRef = useRef(false)

  const [state, setState] = useState<HistoryState>({
    items: [],
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    hasMore: false,
    taskType: taskType,
  })

  const fetchHistory = useCallback(
    async (page = 1, pageSize = 10) => {
      if (!isLoggedIn || !user?.userId) {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: 'Please login to view history',
        }))
        return
      }

      // 如果已经在加载中，不要重复请求
      if (isLoadingRef.current) return

      // 设置加载状态
      isLoadingRef.current = true
      setState((prev) => ({ ...prev, isLoading: true, error: null }))

      try {
        // 使用统一的历史记录API
        const response = await fetch(API_ENDPOINTS.piapi.history, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            userId: user?.userId,
            taskType: taskType,
            page,
            pageSize,
          }),
        })

        if (!response.ok) {
          throw new Error(`API request failed: ${response.statusText}`)
        }

        const result = await response.json()

        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch history')
        }

        const items = result.data || []
        const pagination = result.pagination || {}
        const hasMore = pagination.hasMore || false

        // 转换为统一格式
        const unifiedItems: UnifiedHistoryItem[] = items.map((item: any) => ({
          id: item.id,
          userId: item.user_id,
          taskId: item.external_task_id,
          taskType: item.task_type,
          status: item.status,
          inputParams: item.input_params || {},
          resultData: item.result_data || {},
          metadata: item.metadata || {},
          errorMessage: item.error_message,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          completedAt: item.completed_at,
        }))

        // 确保不会添加重复的项
        if (page === 1) {
          // 第一页：直接替换所有项
          setState((prev) => ({
            ...prev,
            items: unifiedItems,
            isLoading: false,
            currentPage: page,
            totalPages: pagination.totalPages || 1,
            hasMore,
          }))
        } else {
          // 后续页：合并并去重
          setState((prev) => {
            // 创建一个 ID 集合来跟踪已有的项
            const existingIds = new Set(prev.items.map((item) => item.id))

            // 过滤掉已存在的项
            const newItems = unifiedItems.filter(
              (item) => !existingIds.has(item.id)
            )

            return {
              ...prev,
              items: [...prev.items, ...newItems],
              isLoading: false,
              currentPage: page,
              totalPages: pagination.totalPages || 1,
              hasMore,
            }
          })
        }
      } catch (error) {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error:
            error instanceof Error ? error.message : 'Failed to fetch history',
        }))
      } finally {
        // 重置加载状态
        isLoadingRef.current = false
      }
    },
    [isLoggedIn, user, taskType] // 移除 state.isLoading 依赖
  )

  const refreshHistory = useCallback(async () => {
    setState((prev) => ({ ...prev, items: [], currentPage: 1 }))
    await fetchHistory(1)
  }, [fetchHistory])

  return {
    ...state,
    fetchHistory,
    refreshHistory,
  }
}
