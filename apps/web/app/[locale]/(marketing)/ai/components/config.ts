import { TaskConfig, TaskType, FieldMapping, PollingConfig } from './types'
//
// 默认轮询配置
const DEFAULT_POLLING_CONFIG: PollingConfig = {
  interval: 5000, // 5秒
  timeout: 600000, // 10分钟
  maxRetries: 120, // 最大120次重试
}

// 各任务类型的配置
export const TASK_CONFIGS: Record<TaskType, TaskConfig> = {
  'face-swap': {
    taskType: 'face-swap',
    apiProvider: 'piapi',
    mediaType: 'image',
    fieldMapping: {
      input: {
        firstImage: 'swap_image',
        secondImage: 'target_image',
      },
      output: {
        imageUrl: 'image_url',
      },
      labels: {
        firstImage: 'Swap Image',
        secondImage: 'Target Image',
        result: 'Face Swap Result',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['swap_image', 'target_image'],
    },
  },

  ai_try_on: {
    taskType: 'ai_try_on',
    apiProvider: 'kling',
    mediaType: 'image',
    fieldMapping: {
      input: {
        firstImage: 'model_input',
        secondImage: 'dress_input',
      },
      output: {
        imageUrl: 'imageUrl',
      },
      labels: {
        firstImage: 'Person Image',
        secondImage: 'Clothing Image',
        result: 'Try-on Result',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['model_input', 'dress_input'],
    },
  },

  ai_hug: {
    taskType: 'ai_hug',
    apiProvider: 'piapi',
    mediaType: 'video',
    fieldMapping: {
      input: {
        firstImage: 'image_url',
      },
      output: {
        videoUrl: 'video_url',
      },
      labels: {
        firstImage: 'Image',
        result: 'AI Hug Video',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['image_url'],
    },
  },

  aismile: {
    taskType: 'aismile',
    apiProvider: 'kieai',
    mediaType: 'video',
    fieldMapping: {
      input: {
        imageUrl: 'imageUrl',
        prompt: 'prompt',
      },
      output: {
        videoUrl: 'videoUrl',
      },
      labels: {
        firstImage: 'Source Image',
        result: 'AI Smile Video',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['imageUrl'],
    },
    defaultValues: {
      duration: 5,
      quality: '720p',
      aspectRatio: '9:16',
      waterMark: '',
    },
  },

  imagetovideo: {
    taskType: 'imagetovideo',
    apiProvider: 'kieai',
    mediaType: 'video',
    fieldMapping: {
      input: {
        imageUrl: 'imageUrl',
        prompt: 'prompt',
      },
      output: {
        videoUrl: 'videoUrl',
      },
      labels: {
        firstImage: 'Source Image',
        result: 'Generated Video',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['imageUrl', 'prompt'],
    },
    defaultValues: {
      duration: 5,
      quality: '720p',
      aspectRatio: '16:9',
      waterMark: '',
    },
  },

  'photo-to-anime': {
    taskType: 'photo-to-anime',
    apiProvider: 'kieai',
    mediaType: 'image',
    fieldMapping: {
      input: {
        imageUrl: 'filesUrl',
        styleId: 'styleId',
        customPrompt: 'customPrompt',
        aspectRatio: 'aspectRatio',
      },
      output: {
        imageUrl: 'imageUrls',
      },
      labels: {
        firstImage: 'Source Image',
        result: 'Anime Style Image',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['filesUrl', 'styleId'],
    },
    defaultValues: {
      aspectRatio: '1:1',
      nVariants: '1',
    },
  },

  memory_video: {
    taskType: 'memory_video',
    apiProvider: 'piapi',
    mediaType: 'video',
    fieldMapping: {
      input: {
        firstImage: 'image_url',
      },
      output: {
        videoUrl: 'video_url',
      },
      labels: {
        firstImage: 'Photo',
        result: 'Memory Video',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['image_url'],
    },
  },

  'image-expansion': {
    taskType: 'image-expansion',
    apiProvider: 'kieai',
    mediaType: 'image',
    fieldMapping: {
      input: {
        imageUrl: 'filesUrl',
        aspectRatio: 'aspectRatio',
        prompt: 'prompt',
      },
      output: {
        imageUrl: 'imageUrls',
      },
      labels: {
        firstImage: 'Source Image',
        result: 'Expanded Image',
      },
    },
    pollingConfig: DEFAULT_POLLING_CONFIG,
    validation: {
      required: ['filesUrl'],
    },
    defaultValues: {
      aspectRatio: '1:1',
      nVariants: '1',
    },
  },
}

// 根据任务类型获取配置
export const getTaskConfig = (taskType: TaskType): TaskConfig => {
  const config = TASK_CONFIGS[taskType]
  if (!config) {
    throw new Error(`Unsupported task type: ${taskType}`)
  }
  return config
}

// 获取所有支持的任务类型
export const getSupportedTaskTypes = (): TaskType[] => {
  return Object.keys(TASK_CONFIGS) as TaskType[]
}

// 根据API提供商过滤任务类型
export const getTaskTypesByProvider = (
  provider: 'piapi' | 'kieai'
): TaskType[] => {
  return Object.entries(TASK_CONFIGS)
    .filter(([_, config]) => config.apiProvider === provider)
    .map(([taskType, _]) => taskType as TaskType)
}

// API端点配置
export const API_ENDPOINTS = {
  piapi: {
    generate: '/api/aiimage/generate',
    status: '/api/aiimage/task',
    history: '/api/history/getList',
  },
  kieai: {
    aismile: '/api/video/aismile',
    imagetovideo: '/api/video/imagetovideo',
    'photo-to-anime': '/api/images/generate/photo-to-anime',
    status: '/api/video/detail',
    imageStatus: '/api/images/record-info',
    history: '/api/history/getList',
  },
  kling: {
    generate: '/api/kling/try-on',
    status: '/api/kling/try-on',
    history: '/api/history/getList',
  },
} as const
