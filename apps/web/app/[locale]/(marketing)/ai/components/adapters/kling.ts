import { BaseApiAdapter } from './base'
import {
  GenerationRequest,
  GenerationResponse,
  UnifiedTaskDetail,
  TaskType,
} from '../types'
import { API_ENDPOINTS } from '../config'

// Kling API 适配器
export class KlingAdapter extends BaseApiAdapter {
  readonly provider = 'kling' as const

  async generate(request: GenerationRequest): Promise<GenerationResponse> {
    try {
      const { taskType, input } = request

      // 验证输入
      const validation = this.validateInput(input, taskType)
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error || 'Invalid input parameters',
        }
      }

      // 构建Kling API请求体，兼容hook期望的格式
      const requestBody = {
        task_type: taskType,
        input: {
          model_input: input.model_input,
          dress_input: input.dress_input,
          batch_size: input.batch_size || 1,
        },
      }

      const response = await this.makeRequest(API_ENDPOINTS.kling.generate, {
        method: 'POST',
        body: JSON.stringify(requestBody),
      })

      const data = await response.json()

      if (data.code === 200 && data.data?.task_id) {
        return {
          success: true,
          taskId: data.data.task_id,
          message: data.message || 'Task created successfully',
          data: data.data,
        }
      } else {
        return {
          success: false,
          error: data.message || 'Failed to create task',
        }
      }
    } catch (error) {
      return this.handleError(error, 'generate')
    }
  }

  async getTaskStatus(
    taskId: string,
    signal?: AbortSignal,
    taskType?: TaskType
  ): Promise<UnifiedTaskDetail> {
    try {
      const response = await this.makeRequest(
        `${API_ENDPOINTS.kling.status}/${taskId}`,
        { signal }
      )
      const data = await response.json()

      if (data.code === 200 && data.data) {
        return this.formatToUnified(data.data, 'ai_try_on')
      } else {
        throw new Error(data.message || 'Failed to get task status')
      }
    } catch (error) {
      // 如果是取消错误，直接抛出
      if (error instanceof Error && error.name === 'AbortError') {
        throw error
      }

      throw new Error(
        `Failed to get task status: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      )
    }
  }

  formatToUnified(apiResponse: any, taskType: TaskType): UnifiedTaskDetail {
    // 映射Kling状态到统一格式
    const status = this.mapKlingStatus(apiResponse.status)

    return {
      taskId: apiResponse.task_id,
      taskType: taskType,
      status: {
        status,
        progress: apiResponse.status === 'processing' ? 50 : undefined,
        message:
          apiResponse.status === 'failed'
            ? apiResponse.error?.message
            : undefined,
      },
      input: apiResponse.input || {},
      output: {
        imageUrl: apiResponse.output.imageUrl,
      },
      metadata: {
        provider: 'kling',
        model: apiResponse.meta?.model || 'kolors-virtual-try-on-v1',
        createdAt: apiResponse.meta?.created_at
          ? new Date(apiResponse.meta.created_at).toISOString()
          : undefined,
        completedAt:
          apiResponse.meta?.updated_at && status === 'success'
            ? new Date(apiResponse.meta.updated_at).toISOString()
            : undefined,
        ...apiResponse.meta,
      },
      error: apiResponse.error
        ? {
            code: apiResponse.error.code || 'KLING_ERROR',
            message: apiResponse.error.message || 'Unknown error',
          }
        : undefined,
    }
  }

  validateInput(
    input: Record<string, any>,
    taskType: TaskType
  ): { isValid: boolean; error?: string } {
    const baseValidation = super.validateInput(input, taskType)
    if (!baseValidation.isValid) {
      return baseValidation
    }

    // Kling API 特定验证
    switch (taskType) {
      case 'ai_try_on':
        if (!input.model_input) {
          return {
            isValid: false,
            error: 'Virtual try-on requires model_input (person image)',
          }
        }
        if (!input.dress_input) {
          return {
            isValid: false,
            error: 'Virtual try-on requires dress_input (clothing image)',
          }
        }
        break

      default:
        return {
          isValid: false,
          error: `Unsupported task type for Kling API: ${taskType}`,
        }
    }

    return { isValid: true }
  }

  // Kling特有的状态映射
  private mapKlingStatus(
    klingStatus: string
  ): 'pending' | 'processing' | 'success' | 'failed' {
    switch (klingStatus?.toLowerCase()) {
      case 'succeed':
        return 'success'
      case 'failed':
        return 'failed'
      case 'processing':
      case 'submitted':
        return 'processing'
      case 'pending':
      default:
        return 'pending'
    }
  }
}
