import {
  ApiAdapter,
  GenerationRequest,
  GenerationResponse,
  UnifiedTaskDetail,
  TaskType,
} from '../types'

// 抽象基类 - 定义通用行为
export abstract class BaseApiAdapter implements ApiAdapter {
  abstract readonly provider: 'piapi' | 'kieai' | 'kling'

  abstract generate(request: GenerationRequest): Promise<GenerationResponse>
  abstract getTaskStatus(
    taskId: string,
    signal?: AbortSignal,
    taskType?: TaskType
  ): Promise<UnifiedTaskDetail>
  abstract formatToUnified(
    apiResponse: any,
    taskType: TaskType
  ): UnifiedTaskDetail

  // 通用的输入验证方法
  validateInput(
    input: Record<string, any>,
    taskType: TaskType
  ): { isValid: boolean; error?: string } {
    try {
      // 这里可以根据taskType进行特定验证
      // 基础验证：检查必需字段
      if (!input || typeof input !== 'object') {
        return { isValid: false, error: 'Invalid input parameters' }
      }

      return { isValid: true }
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Validation error',
      }
    }
  }

  // 通用HTTP请求方法
  protected async makeRequest(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include',
      ...options,
    }

    const response = await fetch(url, defaultOptions)

    if (!response.ok) {
      const data = await response.json().catch(() => ({}))
      const errorMessage =
        data.msg ||
        data.message ||
        `HTTP ${response.status}: ${response.statusText}`
      throw new Error(errorMessage)
    }

    return response
  }

  // 通用状态映射
  protected mapStatus(
    apiStatus: string | number
  ): 'pending' | 'processing' | 'success' | 'failed' {
    if (typeof apiStatus === 'string') {
      switch (apiStatus.toLowerCase()) {
        case 'success':
        case 'completed':
        case 'done':
          return 'success'
        case 'failed':
        case 'error':
        case 'fail':
          return 'failed'
        case 'processing':
        case 'generating':
        case 'running':
          return 'processing'
        case 'pending':
        case 'waiting':
        case 'queued':
        case 'wait':
        case 'queueing':
        default:
          return 'pending'
      }
    }

    if (typeof apiStatus === 'number') {
      switch (apiStatus) {
        case 200:
          return 'success'
        case 102:
          return 'processing'
        case 101:
        case 100:
          return 'pending'
        default:
          return 'failed'
      }
    }

    return 'pending'
  }

  // 通用错误处理方法
  protected handleError(error: any, operation: string): GenerationResponse {
    console.error(`${operation} error:`, error)

    let errorMessage = `Failed to ${operation}`

    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    // 检查是否是认证或积分错误
    if (
      errorMessage.includes('Authentication required') ||
      errorMessage.includes('401')
    ) {
      errorMessage = 'Authentication required'
    } else if (
      errorMessage.includes('Insufficient credits') ||
      errorMessage.includes('402')
    ) {
      errorMessage = 'Insufficient credits'
    }

    return {
      success: false,
      error: errorMessage,
    }
  }
}
