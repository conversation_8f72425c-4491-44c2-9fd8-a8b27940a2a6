import { ApiAdapter, TaskType } from '../types'
import { getTaskConfig } from '../config'
import { PiApiAdapter } from './piapi'
import { KieAiAdapter } from './kieai'
import { KlingAdapter } from './kling'
//

// 适配器工厂
export class AdapterFactory {
  private static piApiAdapter: PiApiAdapter | null = null
  private static kieAiAdapter: KieAiAdapter | null = null
  private static klingAdapter: KlingAdapter | null = null

  // 根据任务类型获取对应的适配器
  static getAdapter(taskType: TaskType): ApiAdapter {
    const config = getTaskConfig(taskType)

    switch (config.apiProvider) {
      case 'piapi':
        if (!this.piApiAdapter) {
          this.piApiAdapter = new PiApiAdapter()
        }
        return this.piApiAdapter

      case 'kieai':
        if (!this.kieAiAdapter) {
          this.kieAiAdapter = new KieAiAdapter()
        }
        return this.kieAiAdapter

      case 'kling':
        if (!this.klingAdapter) {
          this.klingAdapter = new KlingAdapter()
        }
        return this.klingAdapter

      default:
        throw new Error(`Unsupported API provider: ${config.apiProvider}`)
    }
  }

  // 根据提供商获取适配器
  static getAdapterByProvider(
    provider: 'piapi' | 'kieai' | 'kling'
  ): ApiAdapter {
    switch (provider) {
      case 'piapi':
        if (!this.piApiAdapter) {
          this.piApiAdapter = new PiApiAdapter()
        }
        return this.piApiAdapter

      case 'kieai':
        if (!this.kieAiAdapter) {
          this.kieAiAdapter = new KieAiAdapter()
        }
        return this.kieAiAdapter

      case 'kling':
        if (!this.klingAdapter) {
          this.klingAdapter = new KlingAdapter()
        }
        return this.klingAdapter

      default:
        throw new Error(`Unsupported API provider: ${provider}`)
    }
  }

  // 清除缓存的适配器实例（主要用于测试）
  static clearCache(): void {
    this.piApiAdapter = null
    this.kieAiAdapter = null
    this.klingAdapter = null
  }
}
