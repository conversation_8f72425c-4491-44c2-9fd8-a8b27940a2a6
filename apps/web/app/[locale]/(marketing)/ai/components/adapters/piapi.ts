import { BaseApiAdapter } from './base'
import {
  GenerationRequest,
  GenerationResponse,
  UnifiedTaskDetail,
  TaskType,
} from '../types'
import { API_ENDPOINTS } from '../config'

// PiAPI 适配器
export class PiApiAdapter extends BaseApiAdapter {
  readonly provider = 'piapi' as const

  async generate(request: GenerationRequest): Promise<GenerationResponse> {
    try {
      const { taskType, input } = request

      // 验证输入
      const validation = this.validateInput(input, taskType)
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error || 'Invalid input parameters',
        }
      }

      const response = await this.makeRequest(API_ENDPOINTS.piapi.generate, {
        method: 'POST',
        body: JSON.stringify({
          task_type: taskType,
          input: input,
        }),
      })

      const data = await response.json()

      if (data.code === 200 && data.data?.task_id) {
        return {
          success: true,
          taskId: data.data.task_id,
          message: data.msg,
          data: data.data,
        }
      } else {
        return {
          success: false,
          error: data.msg || 'Failed to create task',
        }
      }
    } catch (error) {
      return this.handleError(error, 'generate')
    }
  }

  async getTaskStatus(
    taskId: string,
    signal?: AbortSignal,
    taskType?: TaskType
  ): Promise<UnifiedTaskDetail> {
    try {
      const response = await this.makeRequest(
        `${API_ENDPOINTS.piapi.status}/${taskId}`,
        { signal }
      )
      const data = await response.json()

      if (data.code === 200 && data.data) {
        return this.formatToUnified(data.data, data.data.task_type)
      } else {
        throw new Error(data.msg || 'Failed to get task status')
      }
    } catch (error) {
      // 如果是取消错误，直接抛出
      if (error instanceof Error && error.name === 'AbortError') {
        throw error
      }

      throw new Error(
        `Failed to get task status: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      )
    }
  }

  formatToUnified(apiResponse: any, taskType: TaskType): UnifiedTaskDetail {
    const status = this.mapStatus(apiResponse.status)

    return {
      taskId: apiResponse.task_id,
      taskType: taskType,
      status: {
        status,
        progress: apiResponse.progress,
        message: apiResponse.detail || undefined,
      },
      input: apiResponse.input || {},
      output: apiResponse.output
        ? {
            imageUrl: apiResponse.output.image_url,
            videoUrl: apiResponse.output.video_url,
            ...apiResponse.output,
          }
        : undefined,
      metadata: {
        provider: 'piapi',
        model: apiResponse.model,
        createdAt: apiResponse.created_at,
        completedAt: apiResponse.completed_at,
        ...apiResponse.meta,
      },
      error: apiResponse.error?.code
        ? {
            code: apiResponse.error.code,
            message: apiResponse.error.message || 'Unknown error',
          }
        : undefined,
    }
  }

  validateInput(
    input: Record<string, any>,
    taskType: TaskType
  ): { isValid: boolean; error?: string } {
    const baseValidation = super.validateInput(input, taskType)
    if (!baseValidation.isValid) {
      return baseValidation
    }

    // PiAPI 特定验证
    switch (taskType) {
      case 'face-swap':
        if (!input.swap_image || !input.target_image) {
          return {
            isValid: false,
            error: 'Face swap requires both swap_image and target_image',
          }
        }
        break

      case 'ai_try_on':
        if (!input.model_input || !input.dress_input) {
          return {
            isValid: false,
            error: 'Virtual try-on requires both model_input and dress_input',
          }
        }
        break

      case 'ai_hug':
        if (!input.image_url) {
          return {
            isValid: false,
            error: 'AI hug requires image_url',
          }
        }
        break

      case 'memory_video':
        if (!input.image_url) {
          return {
            isValid: false,
            error: 'Memory video requires image_url',
          }
        }
        break

      default:
        return {
          isValid: false,
          error: `Unsupported task type for PiAPI: ${taskType}`,
        }
    }

    return { isValid: true }
  }
}
