import { useState, useCallback } from 'react'
import toast from 'react-hot-toast'

interface UploadState {
  isUploading: boolean
  progress: number
  imageUrl: string | null
}

interface UseUploadReturn extends UploadState {
  uploadImage: (file: File) => Promise<void>
  setImageUrl: (url: string) => void
  reset: () => void
}

export function useUpload(): UseUploadReturn {
  const [state, setState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    imageUrl: null,
  })

  const uploadImage = useCallback(async (file: File) => {
    setState((prev) => ({
      ...prev,
      isUploading: true,
      progress: 0,
    }))
    console.log('uploadImage', file)
    try {
      // 真实上传：使用项目现有的上传API
      const imageUrl = await clientDirectUpload(file, (progress) => {
        setState((prev) => ({ ...prev, progress }))
      })

      setState((prev) => ({
        ...prev,
        isUploading: false,
        imageUrl,
        progress: 100,
      }))
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Upload failed'
      toast.error(errorMessage)
      setState((prev) => ({
        ...prev,
        isUploading: false,
        progress: 0,
      }))
    }
  }, [])

  // 客户端直接上传方式 - 复用项目现有逻辑
  const clientDirectUpload = async (
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<string> => {
    try {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        throw new Error('Only image files are supported')
      }

      // 验证文件大小（限制为 10MB）
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size must be less than 10MB')
      }

      onProgress?.(10)

      // 1. 获取签名 URL
      const signatureResponse = await fetch('/api/upload/get-signature', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: file.name,
          contentType: file.type,
        }),
      })

      onProgress?.(30)

      if (!signatureResponse.ok) {
        throw new Error('Failed to get upload signature')
      }

      const signatureData = await signatureResponse.json()

      if (signatureData.code !== 200) {
        throw new Error(signatureData.msg || 'Failed to get upload signature')
      }

      const { url: signedUrl, ossUrl } = signatureData.data

      if (!signedUrl || !ossUrl) {
        throw new Error('Invalid signature data received')
      }

      onProgress?.(50)

      // 2. 使用签名 URL 直接上传文件到 OSS
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      })

      onProgress?.(90)

      if (!uploadResponse.ok) {
        throw new Error('Upload failed')
      }

      onProgress?.(100)

      // 3. 返回上传后的文件 URL
      return ossUrl
    } catch (error) {
      console.error('Client direct upload failed:', error)
      throw error
    }
  }

  const setImageUrl = useCallback((url: string) => {
    setState((prev) => ({
      ...prev,
      imageUrl: url,
      isUploading: false,
      progress: 100,
    }))
  }, [])

  const reset = useCallback(() => {
    setState({
      isUploading: false,
      progress: 0,
      imageUrl: null,
    })
  }, [])

  return {
    ...state,
    uploadImage,
    setImageUrl,
    reset,
  }
}
