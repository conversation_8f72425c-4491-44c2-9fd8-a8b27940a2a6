import { useState, useCallback, useEffect } from 'react'

interface TaskDetail {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  result_url?: string
  error_message?: string
  created_at: string
  completed_at?: string
}

interface UseTaskDetailReturn {
  taskDetail: TaskDetail | null
  isLoading: boolean
  error: string | null
  fetchTaskDetail: (taskId: string) => Promise<void>
  pollTaskDetail: (taskId: string, interval?: number) => void
  stopPolling: () => void
}

export function useTaskDetail(): UseTaskDetailReturn {
  const [taskDetail, setTaskDetail] = useState<TaskDetail | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(
    null
  )

  const fetchTaskDetail = useCallback(async (taskId: string) => {
    setIsLoading(true)
    setError(null)

    try {
      // 调用本地API接口
      const response = await fetch(`/api/aiimage/task/${taskId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // 包含认证cookie
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.code !== 200) {
        throw new Error(result.message || 'Failed to fetch task detail')
      }

      // 映射PiAPI格式到本地TaskDetail格式
      const taskDetail: TaskDetail = {
        task_id: result.data.task_id,
        status: result.data.status,
        result_url: result.data.output?.result_url,
        error_message: result.data.error?.message,
        created_at: result.data.meta?.created_at || new Date().toISOString(),
        completed_at: result.data.meta?.ended_at,
      }

      setTaskDetail(taskDetail)
    } catch (error) {
      setError(
        error instanceof Error ? error.message : 'Failed to fetch task detail'
      )
    } finally {
      setIsLoading(false)
    }
  }, [])

  const pollTaskDetail = useCallback(
    (taskId: string, interval = 2000) => {
      // 清除之前的轮询
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }

      // 立即获取一次
      fetchTaskDetail(taskId)

      // 设置轮询
      const newInterval = setInterval(() => {
        fetchTaskDetail(taskId)
      }, interval)

      setPollingInterval(newInterval)
    },
    [fetchTaskDetail, pollingInterval]
  )

  const stopPolling = useCallback(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      setPollingInterval(null)
    }
  }, [pollingInterval])

  // 当任务完成或失败时自动停止轮询
  useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status === 'completed' || taskDetail.status === 'failed')
    ) {
      stopPolling()
    }
  }, [taskDetail, stopPolling])

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }
    }
  }, [pollingInterval])

  return {
    taskDetail,
    isLoading,
    error,
    fetchTaskDetail,
    pollTaskDetail,
    stopPolling,
  }
}
