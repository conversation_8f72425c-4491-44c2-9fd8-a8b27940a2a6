import { useState, useCallback, useRef, useEffect } from 'react'

interface TaskDetail {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  output: {
    image_url?: string
    image_base64?: string
  }
  error_message?: string
  created_at: string
  completed_at?: string
}

interface FaceSwapState {
  // 生成状态
  isGenerating: boolean
  taskId: string | null
  error: string | null

  // 任务详情状态（来自轮询）
  taskDetail: TaskDetail | null
  isPolling: boolean
  pollingError: string | null
}

interface UseFaceSwapReturn extends FaceSwapState {
  generateFaceSwap: (swapImage: string, targetImage: string) => Promise<void>
  reset: () => void
  stopPolling: () => void
}

export function useFaceSwap(): UseFaceSwapReturn {
  const [state, setState] = useState<FaceSwapState>({
    isGenerating: false,
    taskId: null,
    error: null,
    taskDetail: null,
    isPolling: false,
    pollingError: null,
  })

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const pollingAttemptsRef = useRef<number>(0)
  const maxPollingAttempts = 60 // 2分钟轮询

  // 获取任务详情
  const fetchTaskDetail = useCallback(async (taskId: string) => {
    try {
      const response = await fetch(`/api/aiimage/task/${taskId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.code !== 200) {
        throw new Error(result.message || 'Failed to fetch task detail')
      }

      // 映射PiAPI格式到本地TaskDetail格式
      const taskDetail: TaskDetail = {
        task_id: result.data.task_id,
        status: result.data.status,
        output: result.data.output,
        error_message: result.data.error?.message,
        created_at: result.data.meta?.created_at || new Date().toISOString(),
        completed_at: result.data.meta?.ended_at,
      }

      return taskDetail
    } catch (error) {
      throw new Error(
        error instanceof Error ? error.message : 'Failed to fetch task detail'
      )
    }
  }, [])

  // 开始轮询任务状态
  const startPolling = useCallback(
    (taskId: string) => {
      // 清除之前的轮询
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }

      pollingAttemptsRef.current = 0
      setState((prev) => ({ ...prev, isPolling: true, pollingError: null }))

      // 立即获取一次
      fetchTaskDetail(taskId)
        .then((taskDetail) => {
          setState((prev) => ({ ...prev, taskDetail }))

          // 如果已经完成，不需要继续轮询
          if (
            taskDetail.status === 'completed' ||
            taskDetail.status === 'failed'
          ) {
            setState((prev) => ({ ...prev, isPolling: false }))
            return
          }
        })
        .catch((error) => {
          setState((prev) => ({
            ...prev,
            pollingError: error.message,
          }))
        })

      // 设置定时轮询
      const interval = setInterval(async () => {
        pollingAttemptsRef.current += 1

        // 达到最大轮询次数，停止轮询
        if (pollingAttemptsRef.current >= maxPollingAttempts) {
          clearInterval(interval)
          setState((prev) => ({
            ...prev,
            isPolling: false,
            pollingError: '任务处理超时，请稍后查看历史记录',
          }))
          return
        }

        try {
          const taskDetail = await fetchTaskDetail(taskId)
          setState((prev) => ({ ...prev, taskDetail, pollingError: null }))

          // 任务完成或失败，停止轮询
          if (
            taskDetail.status === 'completed' ||
            taskDetail.status === 'failed'
          ) {
            clearInterval(interval)
            setState((prev) => ({ ...prev, isPolling: false }))
          }
        } catch (error) {
          console.error('Polling error:', error)
          setState((prev) => ({
            ...prev,
            pollingError:
              error instanceof Error ? error.message : 'Polling failed',
          }))
        }
      }, 2000) // 每2秒轮询一次

      pollingIntervalRef.current = interval
    },
    [fetchTaskDetail, maxPollingAttempts]
  )

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }
    setState((prev) => ({ ...prev, isPolling: false }))
  }, [])

  // 生成face swap
  const generateFaceSwap = useCallback(
    async (swapImage: string, targetImage: string) => {
      setState((prev) => ({
        ...prev,
        isGenerating: true,
        error: null,
        taskDetail: null,
        pollingError: null,
      }))

      try {
        // 调用生成接口
        const response = await fetch('/api/aiimage/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            task_type: 'face-swap',
            input: {
              swap_image: swapImage,
              target_image: targetImage,
            },
          }),
        })

        if (!response.ok) {
          throw new Error(`API request failed: ${response.statusText}`)
        }

        const result = await response.json()

        if (result.code !== 200) {
          throw new Error(result.message || 'Generation failed')
        }

        const taskId = result.data.task_id

        setState((prev) => ({
          ...prev,
          isGenerating: false,
          taskId: taskId,
        }))

        // 自动开始轮询任务状态
        startPolling(taskId)
      } catch (error) {
        setState((prev) => ({
          ...prev,
          isGenerating: false,
          error: error instanceof Error ? error.message : 'Generation failed',
        }))
      }
    },
    [startPolling]
  )

  // 重置状态
  const reset = useCallback(() => {
    stopPolling()
    setState({
      isGenerating: false,
      taskId: null,
      error: null,
      taskDetail: null,
      isPolling: false,
      pollingError: null,
    })
  }, [stopPolling])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }
    }
  }, [])

  return {
    ...state,
    generateFaceSwap,
    reset,
    stopPolling,
  }
}
