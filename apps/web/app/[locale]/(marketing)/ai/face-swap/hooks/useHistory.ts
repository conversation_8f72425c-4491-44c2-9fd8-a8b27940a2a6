import { useState, useCallback } from 'react'
import { useAuth } from '../../../../../../modules/ui/hooks/use-auth'
import { HistoryItem } from '@/types/history'

interface HistoryState {
  items: HistoryItem[]
  isLoading: boolean
  error: string | null
  currentPage: number
  totalPages: number
  hasMore: boolean
  taskType: 'face-swap' | 'ai_try_on' | 'ai_hug' | 'aismile' | 'imagetovideo'
}

interface UseHistoryReturn extends HistoryState {
  fetchHistory: (page?: number, pageSize?: number) => Promise<void>
  refreshHistory: () => Promise<void>
}

export function useHistory(
  taskType: 'face-swap' | 'ai_try_on' | 'ai_hug' | 'aismile' | 'imagetovideo'
): UseHistoryReturn {
  const { user, isLoggedIn } = useAuth()

  const [state, setState] = useState<HistoryState>({
    items: [],
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    hasMore: false,
    taskType: taskType,
  })

  const fetchHistory = useCallback(
    async (page = 1, pageSize = 10) => {
      if (!isLoggedIn || !user?.userId) {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: 'Please login to view history',
        }))
        return
      }

      setState((prev) => ({ ...prev, isLoading: true, error: null }))

      try {
        // 调用真实的历史记录API
        const response = await fetch(`/api/history/getList`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            userId: user?.userId,
            taskType: state.taskType,
            page,
            pageSize,
          }),
        })

        if (!response.ok) {
          throw new Error(`API request failed: ${response.statusText}`)
        }

        const result = await response.json()

        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch history')
        }

        const items = result.data || []
        const pagination = result.pagination || {}
        const hasMore = pagination.hasMore || false

        setState((prev) => ({
          ...prev,
          items: page === 1 ? items : [...prev.items, ...items],
          isLoading: false,
          currentPage: page,
          totalPages: pagination.totalPages || 1,
          hasMore,
        }))
      } catch (error) {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error:
            error instanceof Error ? error.message : 'Failed to fetch history',
        }))
      }
    },
    [isLoggedIn, user]
  )

  const refreshHistory = useCallback(async () => {
    setState((prev) => ({ ...prev, items: [], currentPage: 1 }))
    await fetchHistory(1)
  }, [fetchHistory])

  return {
    ...state,
    fetchHistory,
    refreshHistory,
  }
}
