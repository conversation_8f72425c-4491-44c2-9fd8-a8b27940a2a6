'use client'

import React, { useState, useCallback } from 'react'
import { UploadImage } from './components/UploadImage'
import { SampleImages } from './components/SampleImages'
import { GenerateButton } from './components/GenerateButton'
import { Ta<PERSON>, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from '@ui/components/tabs'
import { useAuth } from '../../../../../modules/ui/hooks/use-auth'
import { useUpload } from './hooks/useUpload'

import { HistoryIcon, ImageIcon, RotateCcw } from 'lucide-react'
import { FACE_SWAP_CONFIG } from './config'

// 导入新的统一组件和hooks
import {
  useUnifiedGeneration,
  useUnifiedHistory,
  UnifiedHistoryTab,
  UnifiedTaskDetailModal,
  TaskType,
  GenerationProgress,
  useGenerationProgress,
} from '../components'

const TASK_TYPE: TaskType = 'face-swap'

export default function FaceSwapPage() {
  const { isLoggedIn } = useAuth()
  const [activeTab, setActiveTab] = useState('creations')

  // 进度管理hook - 30秒时间
  const {
    progress: generationProgress,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress: setGenerationProgress,
  } = useGenerationProgress(99, 120000) // 120秒时间

  // 图片上传hooks - 源图片和目标图片
  const swapUpload = useUpload()
  const targetUpload = useUpload()

  // 使用统一的生成hook
  const {
    isGenerating,
    taskId,
    error: generateError,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset: resetGeneration,
    stopPolling,
  } = useUnifiedGeneration(TASK_TYPE)

  // 使用统一的历史记录hook
  const {
    items: historyItems,
    isLoading: isLoadingHistory,
    error: historyError,
    refreshHistory,
  } = useUnifiedHistory(TASK_TYPE)

  // 生成面部交换
  const handleGenerate = useCallback(async () => {
    if (!swapUpload.imageUrl || !targetUpload.imageUrl) {
      return
    }

    try {
      // 切换到 creations tab
      setActiveTab('creations')

      // 开始进度计时器
      startProgress()

      // 使用统一的生成接口
      await generate({
        swap_image: swapUpload.imageUrl,
        target_image: targetUpload.imageUrl,
      })
    } catch (error) {
      console.error('Failed to generate face swap:', error)
      // 生成失败时重置进度
      resetProgress()
    }
  }, [
    swapUpload.imageUrl,
    targetUpload.imageUrl,
    generate,
    startProgress,
    resetProgress,
    setActiveTab,
  ])

  // 重置所有上传的图片
  const handleReset = useCallback(() => {
    swapUpload.reset()
    targetUpload.reset()
    resetGeneration()
    stopPolling()

    // 重置进度状态
    resetProgress()
  }, [swapUpload, targetUpload, resetGeneration, stopPolling, resetProgress])

  // 样例图片选择
  const handleSwapSampleSelect = useCallback(
    (url: string) => {
      swapUpload.setImageUrl(url)
    },
    [swapUpload]
  )

  const handleTargetSampleSelect = useCallback(
    (url: string) => {
      targetUpload.setImageUrl(url)
    },
    [targetUpload]
  )

  // 从历史记录重新生成
  const handleRegenerateFromHistory = useCallback(
    async (input: Record<string, any>) => {
      const swapImage = input.swap_image
      const targetImage = input.target_image

      if (!swapImage || !targetImage) return

      swapUpload.setImageUrl(swapImage)
      targetUpload.setImageUrl(targetImage)

      try {
        // 开始进度计时器
        startProgress()

        await generate({
          swap_image: swapImage,
          target_image: targetImage,
        })
      } catch (error) {
        console.error('Failed to regenerate face swap:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [swapUpload, targetUpload, generate, startProgress, resetProgress]
  )

  // 初始化历史记录
  React.useEffect(() => {
    if (isLoggedIn && historyItems.length === 0) {
      refreshHistory()
    }
  }, [isLoggedIn, historyItems.length, refreshHistory])

  // 监听任务完成，自动刷新历史记录
  React.useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status.status === 'success' ||
        taskDetail.status.status === 'failed')
    ) {
      // 任务完成后延迟刷新历史记录，确保webhook已更新数据库
      setTimeout(() => {
        refreshHistory()
      }, 1000)
    }
  }, [taskDetail?.status.status, refreshHistory])

  // 监听任务完成状态，更新进度到100%
  React.useEffect(() => {
    if (taskDetail?.status.status === 'success') {
      // 任务成功完成，将进度设置为100%并停止计时器
      setGenerationProgress(100)
      stopProgress()
    } else if (taskDetail?.status.status === 'failed') {
      // 任务失败，重置进度
      resetProgress()
    }
  }, [
    taskDetail?.status.status,
    setGenerationProgress,
    stopProgress,
    resetProgress,
  ])

  // 适配现有的ResultDisplay组件
  const adaptedTaskDetail = taskDetail
    ? {
        taskId: taskDetail.taskId,
        status:
          taskDetail.status.status === 'success'
            ? 'completed'
            : taskDetail.status.status === 'failed'
            ? 'failed'
            : taskDetail.status.status === 'processing'
            ? 'processing'
            : 'pending',
        inputParams: taskDetail.input,
        resultData: taskDetail.output,
        error: taskDetail.error?.message,
      }
    : null

  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      {/* Main Layout Container */}
      <div className="h-full flex bg-white border-t border-gray-200">
        {/* Left Sidebar - Operations Area */}
        <div className="w-[480px] border-r border-gray-200 bg-white flex flex-col">
          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              <div className="w-full">
                {/* Upload Original Image Section */}
                <div className="space-y-4">
                  <div className="w-full flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Upload Original Image
                    </h3>
                    {/* Reset Button */}
                    <button
                      onClick={handleReset}
                      className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
                    >
                      <RotateCcw className="h-4 w-4" />
                      <span>reset all</span>
                    </button>
                  </div>

                  <div className="h-28">
                    <UploadImage
                      title="Upload Image"
                      imageUrl={targetUpload.imageUrl}
                      isUploading={targetUpload.isUploading}
                      progress={targetUpload.progress}
                      onUpload={targetUpload.uploadImage}
                      featureType="face-swap"
                    />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">
                      Try These Images
                    </p>

                    <SampleImages
                      onSelectSample={handleTargetSampleSelect}
                      showBadges={true}
                      imageCount={4}
                      className="grid-cols-4"
                      images={FACE_SWAP_CONFIG.ui.sampleOriginalImages}
                    />
                  </div>
                </div>

                {/* Divider */}
                <div className="border-t-2 border-gray-200/50 my-8"></div>

                {/* Upload Target Image Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Upload Target Image
                  </h3>
                  <div className="h-28">
                    <UploadImage
                      title="Upload Face"
                      imageUrl={swapUpload.imageUrl}
                      isUploading={swapUpload.isUploading}
                      progress={swapUpload.progress}
                      onUpload={swapUpload.uploadImage}
                      featureType="face-swap"
                    />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">
                      Try These Images
                    </p>
                    <SampleImages
                      onSelectSample={handleSwapSampleSelect}
                      showBadges={false}
                      imageCount={4}
                      className="grid-cols-4"
                      images={FACE_SWAP_CONFIG.ui.sampleTargetImages}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Fixed Generate Button */}
          <GenerateButton
            swapImageUrl={swapUpload.imageUrl}
            targetImageUrl={targetUpload.imageUrl}
            isGenerating={isGenerating}
            onGenerate={handleGenerate}
            generateError={generateError}
          />
        </div>

        {/* Right Main Content Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            {/* Tab 切换头部 */}
            <div className="bg-white flex justify-center">
              <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
                <TabsTrigger
                  value="creations"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <ImageIcon className="h-4 w-4" />
                  <span>Creations</span>
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <HistoryIcon className="h-4 w-4" />
                  <span>History</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Tab 内容 */}
            <TabsContent
              value="creations"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full overflow-y-auto p-4 relative">
                <div className="min-h-full flex items-start justify-center">
                  <div className="w-full">
                    {/* Header Section */}
                    <div className="text-center mb-12">
                      <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        Swap Faces Seamlessly Using AI
                      </h1>
                      <p className="text-lg text-gray-600">
                        Explore endless possibilities and fun of face swapping
                      </p>
                    </div>

                    {/* Result Display Area */}
                    <div className="flex justify-center">
                      <div className="w-full max-w-2xl">
                        {/* Show unified result display */}
                        {isGenerating || isPolling ? (
                          <GenerationProgress
                            progress={generationProgress}
                            isGenerating={isGenerating || isPolling}
                            title="Generating Face Swap"
                            description="AI is swapping faces between your images..."
                            size="md"
                          />
                        ) : generateError || pollingError ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                                <RotateCcw className="w-8 h-8 text-red-500" />
                              </div>
                              <div className="space-y-2">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  Generation Failed
                                </h3>
                                <p className="text-sm text-red-600">
                                  {generateError || pollingError}
                                </p>
                              </div>
                              <button
                                onClick={() => {
                                  resetGeneration()
                                  stopPolling()
                                  resetProgress()
                                }}
                                className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                              >
                                Try Again
                              </button>
                            </div>
                          </div>
                        ) : adaptedTaskDetail?.status === 'completed' &&
                          adaptedTaskDetail?.resultData?.image_url ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="space-y-6">
                              <div className="relative max-w-md mx-auto">
                                <img
                                  src={adaptedTaskDetail.resultData.image_url}
                                  alt="Face swap result"
                                  className="w-full h-[500px] rounded-lg shadow-lg object-contain"
                                />
                              </div>
                              <div className="flex justify-center space-x-4">
                                <button
                                  onClick={() => {
                                    // Download functionality
                                    const a = document.createElement('a')
                                    a.href =
                                      adaptedTaskDetail.resultData?.image_url ||
                                      ''
                                    a.download = `face_swap_${Date.now()}.jpg`
                                    a.click()
                                  }}
                                  className="flex items-center space-x-2 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
                                >
                                  <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  <span>Download Image</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span className="text-2xl">🔄</span>
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900">
                                Ready to Swap Faces
                              </h3>
                              <p className="text-sm text-gray-600">
                                Upload two images and click generate to create
                                amazing face swaps with AI
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="w-full h-full">
                {/* 使用新的统一历史记录组件 */}
                <UnifiedHistoryTab
                  taskType={TASK_TYPE}
                  onRegenerate={handleRegenerateFromHistory}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </main>
  )
}
