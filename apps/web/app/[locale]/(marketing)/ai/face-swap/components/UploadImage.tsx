'use client'

import React, { useCallback, useState, useRef } from 'react'
import { createPortal } from 'react-dom'
import { Button } from '../../../../../../modules/ui/components/button'
import { Progress } from '../../../../../../modules/ui/components/progress'
import toast from 'react-hot-toast'
import { FACE_SWAP_CONFIG } from '../config'
import { VIRTUAL_TRY_ON_CONFIG } from '../../virtual-try-on/config'

type FeatureType =
  | 'face-swap'
  | 'virtual-try-on'
  | 'imagetovideo'
  | 'memory-video'

type ValidationConfig = {
  maxFileSize: number
  allowedTypes: readonly string[]
  allowedExtensions: string[]
  errorMessages: {
    invalidFileType: string
    fileTooLarge: string
  }
}

interface UploadImageProps {
  title?: string
  imageUrl?: string | null
  isUploading?: boolean
  progress?: number
  onUpload: (file: File, url?: string) => void
  onRemove?: () => void
  className?: string
  featureType?: FeatureType
  customValidation?: Partial<ValidationConfig>
  acceptedTypes?: string[]
  maxSizeInMB?: number
}

export function UploadImage({
  title = 'Upload Image',
  imageUrl,
  isUploading = false,
  progress = 0,
  onUpload,
  onRemove,
  className = '',
  featureType = 'face-swap',
  customValidation,
  acceptedTypes,
  maxSizeInMB,
}: UploadImageProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 })

  const getValidationConfig = useCallback((): ValidationConfig => {
    let baseConfig: ValidationConfig

    if (featureType === 'face-swap') {
      baseConfig = {
        maxFileSize: FACE_SWAP_CONFIG.upload.maxFileSize,
        allowedTypes: FACE_SWAP_CONFIG.upload.allowedTypes,
        allowedExtensions: ['PNG', 'JPG', 'WebP'],
        errorMessages: {
          invalidFileType: FACE_SWAP_CONFIG.messages.errors.invalidFileType,
          fileTooLarge: FACE_SWAP_CONFIG.messages.errors.fileTooLarge,
        },
      }
    } else if (featureType === 'virtual-try-on') {
      baseConfig = {
        maxFileSize: VIRTUAL_TRY_ON_CONFIG.upload.maxFileSize,
        allowedTypes: VIRTUAL_TRY_ON_CONFIG.upload.allowedTypes,
        allowedExtensions: ['PNG', 'JPG'],
        errorMessages: {
          invalidFileType:
            VIRTUAL_TRY_ON_CONFIG.messages.errors.invalidFileType,
          fileTooLarge: VIRTUAL_TRY_ON_CONFIG.messages.errors.fileTooLarge,
        },
      }
    } else {
      // 默认配置
      baseConfig = {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
        allowedExtensions: ['PNG', 'JPG', 'WebP'],
        errorMessages: {
          invalidFileType: 'Please select a valid image file',
          fileTooLarge: 'File size cannot exceed 10MB',
        },
      }
    }

    // 如果提供了自定义参数，优先使用
    if (acceptedTypes) {
      baseConfig.allowedTypes = acceptedTypes as readonly string[]
    }
    if (maxSizeInMB) {
      baseConfig.maxFileSize = maxSizeInMB * 1024 * 1024
    }

    return {
      ...baseConfig,
      ...customValidation,
      errorMessages: {
        ...baseConfig.errorMessages,
        ...customValidation?.errorMessages,
      },
    }
  }, [featureType, customValidation, acceptedTypes, maxSizeInMB])

  const validateFile = useCallback(
    (file: File): { isValid: boolean; error?: string } => {
      const config = getValidationConfig()

      if (!config.allowedTypes.includes(file.type)) {
        return {
          isValid: false,
          error: config.errorMessages.invalidFileType,
        }
      }

      if (file.size > config.maxFileSize) {
        return {
          isValid: false,
          error: config.errorMessages.fileTooLarge,
        }
      }

      return { isValid: true }
    },
    [getValidationConfig]
  )

  const getFileRestrictionText = useCallback((): string => {
    const config = getValidationConfig()
    const extensions = config.allowedExtensions.join(', ')
    const maxSizeMB = Math.round(config.maxFileSize / (1024 * 1024))
    return `${extensions} • Max ${maxSizeMB}MB`
  }, [getValidationConfig])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragOver(false)

      const files = Array.from(e.dataTransfer.files)
      const file = files[0]

      if (file && file.type.startsWith('image/')) {
        const validation = validateFile(file)
        if (validation.isValid) {
          const url = URL.createObjectURL(file)
          onUpload(file, url)
        } else {
          toast.error(validation.error || 'File validation failed')
        }
      } else {
        toast.error('Please select a valid image file')
      }
    },
    [onUpload, validateFile, toast]
  )

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (file) {
        const validation = validateFile(file)
        if (validation.isValid) {
          const url = URL.createObjectURL(file)
          onUpload(file, url)
        } else {
          toast.error(validation.error || 'File validation failed')
        }
      }
      e.target.value = ''
    },
    [onUpload, validateFile]
  )

  const handleClick = useCallback(() => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const validation = validateFile(file)
        if (validation.isValid) {
          const url = URL.createObjectURL(file)
          onUpload(file, url)
        } else {
          toast.error(validation.error || 'File validation failed')
        }
      }
    }
    input.click()
  }, [onUpload, validateFile])

  const handleRemove = useCallback(() => {
    if (onRemove) {
      onRemove()
    }
  }, [onRemove])

  const updatePreviewPosition = useCallback(() => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect()
      setPreviewPosition({
        x: rect.right + 16,
        y: rect.top,
      })
    }
  }, [])

  const handleMouseEnter = useCallback(() => {
    updatePreviewPosition()
    setIsHovered(true)
  }, [updatePreviewPosition])

  if (imageUrl) {
    return (
      <>
        <div
          ref={containerRef}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={() => setIsHovered(false)}
          className={`relative ${className} bg-blue-50 flex items-center group object-contain transition-transform duration-300 group-hover:scale-105 justify-center border-dashed border border-blue-500 hover:border-gray-300 hover:shadow-md shadow-sm rounded-lg cursor-pointer`}
        >
          <div
            className="relative overflow-hidden h-28 w-full flex items-center justify-center"
            onClick={handleClick}
          >
            <img
              src={imageUrl}
              alt="Uploaded"
              className="h-full max-w-full object-contain mx-auto"
            />
          </div>
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center rounded-lg">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
              <Button
                onClick={(e) => {
                  e.stopPropagation()
                  handleClick()
                }}
                size="sm"
                className="bg-white/90 text-gray-900 hover:bg-white border-0 shadow-lg"
              >
                Replace
              </Button>
              {onRemove && (
                <Button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleRemove()
                  }}
                  size="sm"
                  variant="error"
                  className="bg-red-500/90 text-white hover:bg-red-600 border-0 shadow-lg"
                >
                  Remove
                </Button>
              )}
            </div>
          </div>
          {isUploading && (
            <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
              <div className="bg-white rounded-lg p-4 max-w-xs w-full mx-4">
                <div className="text-center">
                  <div className="w-8 h-8 mx-auto bg-pink-100 rounded-full flex items-center justify-center mb-2">
                    <svg
                      className="w-4 h-4 text-pink-500 animate-spin"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                  </div>
                  <p className="text-sm text-gray-900 mb-2">Uploading...</p>
                  <Progress value={progress} className="h-2" />
                  <p className="text-xs text-gray-600 mt-1">{progress}%</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {isHovered &&
          typeof window !== 'undefined' &&
          createPortal(
            <div
              className="fixed z-[9999] bg-white rounded-lg shadow-xl border border-gray-200 p-4 pointer-events-none"
              style={{
                left: previewPosition.x,
                top: previewPosition.y,
              }}
            >
              <img
                src={imageUrl}
                alt="Full preview"
                className="max-w-xs max-h-96 object-contain"
              />
            </div>,
            document.body
          )}
      </>
    )
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div
        className={`
          relative rounded-lg text-center cursor-pointer w-full border-dashed border
          transition-all duration-300 ease-in-out bg-blue-50 shadow-sm h-28 flex items-center justify-center
          ${
            isDragOver
              ? 'border-2  border-pink-400 bg-pink-50/50 shadow-md scale-102'
              : ' border-blue-500 hover:border-gray-300 hover:shadow-md'
          }
          ${isUploading ? 'pointer-events-none' : ''}
          ${className}
        `}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        {isUploading ? (
          <div className="flex flex-col items-center justify-center h-full w-full">
            <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center mb-2">
              <svg
                className="w-4 h-4 text-pink-500 animate-spin"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 mb-1">
              Uploading...
            </p>
            <div className="w-20">
              <Progress value={progress} className="h-1.5" />
              <p className="text-xs text-gray-500 mt-1 text-center">
                {progress}%
              </p>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full w-full">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-300 mb-2 ${
                isDragOver
                  ? 'bg-pink-500 text-white'
                  : 'bg-gray-100 text-gray-400'
              }`}
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 mb-1">{title}</p>
            <p className="text-xs text-gray-500">{getFileRestrictionText()}</p>
          </div>
        )}
      </div>
    </div>
  )
}
