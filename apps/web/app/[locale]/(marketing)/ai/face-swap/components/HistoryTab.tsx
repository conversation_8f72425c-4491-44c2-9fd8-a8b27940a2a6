'use client'

import React, { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Calendar, Download, Trash2, RefreshCw } from 'lucide-react'
import { getUserIdFromCookie } from '@/utils/lib'
import { HistoryItem } from '@/types/history'
import toast from 'react-hot-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@ui/components/alert-dialog'

// 字段映射接口定义
interface FieldMapping {
  firstImage: string // 第一张输入图片字段名 (input_params 中)
  secondImage?: string // 第二张输入图片字段名 (input_params 中) - 可选，支持单图片场景
  resultImage?: string // 结果图片字段名 (result_data 中) - 可选，用于图片结果
  resultVideo?: string // 结果视频字段名 (result_data 中) - 可选，用于视频结果
  coverImage?: string // 视频封面图片字段名 (result_data 中) - 可选
  taskType?: 'image' | 'video' // 任务类型，决定显示方式
  labels?: {
    firstImage: string // 第一张图片显示标签
    secondImage?: string // 第二张图片显示标签 - 可选
    result?: string // 结果显示标签
  }
}

// 默认字段映射（保持 face-swap 向后兼容）
const DEFAULT_FIELD_MAPPING: FieldMapping = {
  firstImage: 'swap_image',
  secondImage: 'target_image',
  resultImage: 'result_url',
  labels: {
    firstImage: 'Swap face',
    secondImage: 'Target face',
  },
}

const STATUS_COLORS: Record<HistoryItem['status'], string> = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  PROCESSING: 'bg-blue-100 text-blue-800',
  SUCCESS: 'bg-green-100 text-green-800',
  FAILED: 'bg-red-100 text-red-800',
}

interface HistoryTabProps {
  items: HistoryItem[]
  isLoading?: boolean
  error?: string | null
  currentPage: number
  totalPages: number
  hasMore: boolean
  onLoadMore: () => void
  onRefresh: () => void
  onSelectItem?: (item: HistoryItem) => void
  className?: string
  fieldMapping?: FieldMapping // 新增：可选的字段映射配置
}

export function HistoryTab({
  items,
  isLoading = false,
  error,
  currentPage,
  totalPages,
  hasMore,
  onLoadMore,
  onRefresh,
  onSelectItem,
  className = '',
  fieldMapping = DEFAULT_FIELD_MAPPING, // 使用默认映射
}: HistoryTabProps) {
  const t = useTranslations()
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteItemId, setDeleteItemId] = useState<number | null>(null)

  // 本地化状态标签
  const getStatusLabel = (status: HistoryItem['status']): string => {
    switch (status) {
      case 'PENDING':
        return t('ImageHistory.status.pending', { fallback: 'Pending' })
      case 'PROCESSING':
        return t('ImageHistory.status.processing', { fallback: 'Processing' })
      case 'SUCCESS':
        return t('ImageHistory.status.success', { fallback: 'Success' })
      case 'FAILED':
        return t('ImageHistory.status.failed', { fallback: 'Failed' })
      default:
        return status
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return (
      date.toLocaleDateString() +
      ' ' +
      date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      })
    )
  }

  // 打开删除确认对话框
  const openDeleteDialog = (id: number) => {
    setDeleteItemId(id)
    setShowDeleteDialog(true)
  }

  // 删除历史记录
  const deleteHistoryItem = async () => {
    if (!deleteItemId) return

    try {
      const userId = getUserIdFromCookie()
      if (!userId) {
        throw new Error('User not logged in')
      }

      const response = await fetch('/api/history/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: [deleteItemId],
          userId,
          hardDelete: false, // 软删除
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error('Failed to delete history item')
      }

      toast.success(t('ImageHistory.deleteSuccess'))
      // 刷新列表
      onRefresh()
    } catch (err) {
      console.error('Failed to delete history item:', err)
      toast.error(t('ImageDetail.deleteError'))
    } finally {
      setShowDeleteDialog(false)
      setDeleteItemId(null)
    }
  }

  // 下载图片
  const downloadImage = async (imageUrl: string, index?: number) => {
    try {
      const timestamp = Date.now()
      const filename =
        index !== undefined
          ? `history-image-${index + 1}-${timestamp}.png`
          : `history-image-${timestamp}.png`

      const response = await fetch(
        `/api/download?url=${encodeURIComponent(
          imageUrl
        )}&filename=${encodeURIComponent(filename)}`,
        { method: 'GET' }
      )

      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`)
      }

      const blob = await response.blob()
      const blobUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)

      toast.success('Image downloaded successfully')
    } catch (error) {
      console.error('Error downloading image:', error)
      toast.error('Failed to download image')
    }
  }

  // 获取嵌套对象属性的辅助函数
  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  // 获取图片 URL 的辅助函数
  const getImageUrl = (
    item: HistoryItem,
    imageType: 'first' | 'second' | 'result'
  ): string | null => {
    switch (imageType) {
      case 'first':
        return item.input_params?.[fieldMapping.firstImage] || null
      case 'second':
        return fieldMapping.secondImage
          ? item.input_params?.[fieldMapping.secondImage] || null
          : null
      case 'result':
        return fieldMapping.resultImage
          ? item.result_data?.[fieldMapping.resultImage] || null
          : null
      default:
        return null
    }
  }

  // 获取视频 URL 的辅助函数
  const getVideoUrl = (item: HistoryItem): string | null => {
    if (!fieldMapping.resultVideo) return null
    return getNestedValue(item.result_data, fieldMapping.resultVideo) || null
  }

  // 获取封面图片 URL 的辅助函数
  const getCoverImageUrl = (item: HistoryItem): string | null => {
    if (!fieldMapping.coverImage) return null
    return getNestedValue(item.result_data, fieldMapping.coverImage) || null
  }

  // 获取图片标签的辅助函数
  const getImageLabel = (imageType: 'first' | 'second' | 'result'): string => {
    switch (imageType) {
      case 'first':
        return fieldMapping.labels?.firstImage || 'First image'
      case 'second':
        return fieldMapping.labels?.secondImage || 'Second image'
      case 'result':
        return fieldMapping.labels?.result || 'Result'
      default:
        return ''
    }
  }

  // 下载视频
  const downloadVideo = async (videoUrl: string, index?: number) => {
    try {
      const timestamp = Date.now()
      const filename =
        index !== undefined
          ? `history-video-${index + 1}-${timestamp}.mp4`
          : `history-video-${timestamp}.mp4`

      const response = await fetch(
        `/api/download?url=${encodeURIComponent(
          videoUrl
        )}&filename=${encodeURIComponent(filename)}`,
        { method: 'GET' }
      )

      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`)
      }

      const blob = await response.blob()
      const blobUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)

      toast.success('Video downloaded successfully')
    } catch (error) {
      console.error('Error downloading video:', error)
      toast.error('Failed to download video')
    }
  }

  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-full text-gray-500">
      <Calendar className="h-12 w-12 mb-4 text-gray-400" />
      <p className="text-lg font-medium mb-2">No history found</p>
      <p className="text-sm">
        Start generating images to see your history here
      </p>
    </div>
  )

  const renderLoadingState = () => (
    <div className="flex flex-col items-center justify-center h-64">
      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
      <p className="text-gray-600">Loading history...</p>
    </div>
  )

  if (error) {
    return (
      <div
        className={`flex flex-col items-center justify-center h-64 ${className}`}
      >
        <div className="text-red-500 mb-4">
          <svg
            className="w-12 h-12"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={onRefresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">History</h3>
          <p className="text-sm text-gray-600">Total: {items.length} items</p>
        </div>
        <button
          onClick={onRefresh}
          disabled={isLoading}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50"
          title="Refresh"
        >
          <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* 历史记录列表 */}
      <div className="flex-1 overflow-y-auto">
        {isLoading && items.length === 0 ? (
          renderLoadingState()
        ) : items.length === 0 ? (
          renderEmptyState()
        ) : (
          <div className="space-y-4">
            {items.map((item) => (
              <div
                key={item.id}
                className={`bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow ${
                  onSelectItem ? 'cursor-pointer' : ''
                }`}
                onClick={() => onSelectItem?.(item)}
              >
                {/* 状态和时间 */}
                <div className="flex items-center justify-between mb-3">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      STATUS_COLORS[item.status]
                    }`}
                  >
                    {getStatusLabel(item.status)}
                  </span>
                  <span className="text-xs text-gray-500 flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formatDate(item.created_at)}
                  </span>
                </div>

                {/* 任务ID */}
                {item.external_task_id && (
                  <div className="mb-3">
                    <span className="text-xs text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded">
                      {item.external_task_id}
                    </span>
                  </div>
                )}

                {/* 图片展示 */}
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-4 items-center">
                    {/* 第一张图片 */}
                    {getImageUrl(item, 'first') && (
                      <div className="flex flex-col items-center">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">
                          {getImageLabel('first')}:
                        </h4>
                        <div className="relative group max-w-[120px]">
                          <img
                            src={getImageUrl(item, 'first')!}
                            alt={getImageLabel('first')}
                            className="w-full aspect-square object-cover rounded-md border border-gray-200 max-h-[120px]"
                          />
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-md flex items-center justify-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                downloadImage(getImageUrl(item, 'first')!)
                              }}
                              className="p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors"
                              title="Download"
                            >
                              <Download className="h-4 w-4 text-gray-700" />
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* 箭头 */}
                    {fieldMapping.secondImage &&
                      getImageUrl(item, 'second') && (
                        <div className="flex items-center text-gray-400 self-end mb-8">
                          <svg
                            className="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}

                    {/* 第二张图片 */}
                    {fieldMapping.secondImage &&
                      getImageUrl(item, 'second') && (
                        <div className="flex flex-col items-center">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            {getImageLabel('second')}:
                          </h4>
                          <div className="relative group max-w-[120px]">
                            <img
                              src={getImageUrl(item, 'second')!}
                              alt={getImageLabel('second')}
                              className="w-full aspect-square object-cover rounded-md border border-gray-200 max-h-[120px]"
                            />
                            <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-md flex items-center justify-center">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  downloadImage(getImageUrl(item, 'second')!)
                                }}
                                className="p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors"
                                title="Download"
                              >
                                <Download className="h-4 w-4 text-gray-700" />
                              </button>
                            </div>
                          </div>
                        </div>
                      )}

                    {/* 箭头到结果 */}
                    {(getImageUrl(item, 'result') || getVideoUrl(item)) && (
                      <div className="flex items-center text-gray-400 self-end mb-8">
                        <svg
                          className="w-5 h-5"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    )}

                    {/* 结果图片 */}
                    {getImageUrl(item, 'result') && (
                      <div className="flex flex-col items-center">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">
                          {getImageLabel('result')}:
                        </h4>
                        <div className="relative group max-w-[120px]">
                          <img
                            src={getImageUrl(item, 'result')!}
                            alt={getImageLabel('result')}
                            className="w-full aspect-square object-cover rounded-md border border-gray-200 max-h-[120px]"
                          />
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-md flex items-center justify-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                downloadImage(getImageUrl(item, 'result')!)
                              }}
                              className="p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors"
                              title="Download"
                            >
                              <Download className="h-4 w-4 text-gray-700" />
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* 结果视频 */}
                    {getVideoUrl(item) && (
                      <div className="flex flex-col items-center">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">
                          {getImageLabel('result')}:
                        </h4>
                        <div className="relative group max-w-[180px]">
                          <video
                            src={getVideoUrl(item)!}
                            controls
                            muted
                            className="w-full aspect-video object-cover rounded-md border border-gray-200 max-h-[120px]"
                            poster={getCoverImageUrl(item) || undefined}
                          />
                          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                downloadVideo(getVideoUrl(item)!)
                              }}
                              className="p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors shadow-sm"
                              title="Download Video"
                            >
                              <Download className="h-4 w-4 text-gray-700" />
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center justify-end mt-3 pt-3 border-t border-gray-100">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      openDeleteDialog(item.id)
                    }}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </button>
                </div>
              </div>
            ))}

            {/* 加载更多按钮 */}
            {hasMore && (
              <div className="flex justify-center pt-4">
                <button
                  onClick={onLoadMore}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-600 mr-2 inline-block"></div>
                      Loading...
                    </>
                  ) : (
                    `Load More (${currentPage} of ${totalPages})`
                  )}
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="bg-white border border-gray-200 shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-gray-900 text-lg font-semibold">
              {t('ImageDetail.delete', { fallback: 'Delete' })}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600">
              {t('ImageDetail.deleteConfirm', {
                fallback: 'Are you sure you want to delete this item?',
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-gray-100 text-gray-700 hover:bg-gray-200 border-gray-300"
              onClick={() => setShowDeleteDialog(false)}
            >
              {t('common.confirmation.cancel', { fallback: 'Cancel' })}
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-500 hover:bg-red-600 text-white border-red-500 hover:border-red-600"
              onClick={deleteHistoryItem}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

// 导出 FieldMapping 接口供其他模块使用
export type { FieldMapping }

// 预定义的字段映射配置
export const AI_SMILE_FIELD_MAPPING: FieldMapping = {
  firstImage: 'imageUrl',
  resultVideo: 'videoUrl',
  coverImage: 'videoInfo.imageUrl',
  taskType: 'video',
  labels: {
    firstImage: 'Input Image',
    result: 'Generated Video',
  },
}

export const IMAGE_TO_VIDEO_FIELD_MAPPING: FieldMapping = {
  firstImage: 'imageUrl',
  resultVideo: 'videoUrl',
  coverImage: 'videoInfo.imageUrl',
  taskType: 'video',
  labels: {
    firstImage: 'Input Image',
    result: 'Generated Video',
  },
}
