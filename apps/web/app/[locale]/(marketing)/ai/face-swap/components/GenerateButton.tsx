'use client'

import React, { useEffect } from 'react'
import { Button } from '../../../../../../modules/ui/components/button'
import { useTranslations } from 'next-intl'
import { useToast } from '../../../../../../modules/ui/hooks/use-toast'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'
import PointIcon from '../../components/ui/PointIcon'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useModal } from '@shared/hooks/useModal'

interface GenerateButtonProps {
  swapImageUrl?: string | null
  targetImageUrl?: string | null
  isGenerating?: boolean
  onGenerate: () => Promise<void>
  className?: string
  generateError?: string | null
  requireTwoImages?: boolean
  pointParams?: any
}

export function GenerateButton({
  isGenerating = false,
  onGenerate,
  className = '',
  generateError,
  pointParams,
}: GenerateButtonProps) {
  const t = useTranslations()
  const { toast } = useToast()

  const { calculatePoints, updateUserInfo } = usePermissionCheck()

  const { showLoginModal } = useModal()
  const user = getUserFromClientCookies()

  // 监听生成错误并显示 toast 提示
  useEffect(() => {
    if (generateError) {
      toast({
        variant: 'error',
        title: 'Generation Failed',
        description: generateError,
      })
    }
  }, [generateError, toast])

  const handleClick = () => {
    if (!user) {
      showLoginModal()
      return
    }

    // 执行生成
    onGenerate().then(() => {
      updateUserInfo()
    })
  }

  const isDisabled = isGenerating

  return (
    <div className="sticky bottom-0 left-0 right-0 w-full bg-white border-t border-gray-200 p-4 z-10">
      <Button
        onClick={handleClick}
        disabled={isDisabled}
        className={`
          w-full h-12 text-base font-semibold transition-all duration-200 shadow-md
          hover:scale-[1.02] hover:shadow-lg active:scale-[0.98] gap-1
          ${
            isDisabled
              ? 'opacity-60 cursor-not-allowed bg-gray-200 text-gray-400 hover:bg-gray-200 border-gray-200'
              : 'w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-lg hover:shadow-md transition-all duration-200'
          }
        `}
      >
        {isGenerating && (
          <svg
            className="animate-spin -ml-1 mr-3 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {isGenerating
          ? t('ImageStyleConverter.generating')
          : t('ImageStyleConverter.generateButton')}
        <PointIcon points={calculatePoints(pointParams)} />
      </Button>
    </div>
  )
}
