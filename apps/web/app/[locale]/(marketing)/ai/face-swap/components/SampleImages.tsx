'use client'

import React from 'react'
import { SampleImage } from '../config'

interface SampleImagesProps {
  onSelectSample: (url: string) => void
  className?: string
  title?: string
  showBadges?: boolean
  imageCount?: number
  images: readonly SampleImage[]
}

export function SampleImages({
  onSelectSample,
  className = 'grid-cols-4',
  showBadges = false,
  imageCount = 4,
  images = [],
}: SampleImagesProps) {
  const imagesToShow = images?.slice(0, imageCount)

  return (
    <div>
      <div className={`grid gap-2 ${className}`}>
        {imagesToShow.map((image) => (
          <button
            key={image.id}
            onClick={() => onSelectSample(image.url)}
            className="group relative overflow-hidden rounded-lg border border-gray-200 hover:border-blue-400 transition-all duration-200 hover:shadow-sm hover:scale-105"
          >
            <img
              src={image.url}
              alt={image.alt}
              className="w-full h-full object-cover transition-transform duration-200"
              loading="lazy"
            />
            {showBadges && image.badge && (
              <div className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-xs px-1 py-0.5 rounded font-medium">
                {image.badge}
              </div>
            )}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center shadow-md">
                  <svg
                    className="w-2.5 h-2.5 text-blue-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}
