'use client'

import React from 'react'
import { Card } from '../../../../../../modules/ui/components/card'
import { Button } from '../../../../../../modules/ui/components/button'
import { Skeleton } from '../../../../../../modules/ui/components/skeleton'

interface TaskDetail {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  output: {
    image_url?: string
    image_base64?: string
  }
  error_message?: string
  created_at: string
  completed_at?: string
}

interface ResultDisplayProps {
  taskDetail?: TaskDetail | null
  isLoading?: boolean
  error?: string | null
  onRetry?: () => void
  onDownload?: (url: string) => void
  className?: string
}

export function ResultDisplay({
  taskDetail,
  isLoading = false,
  error,
  onRetry,
  onDownload,
  className = '',
}: ResultDisplayProps) {
  const handleDownload = () => {
    if (taskDetail?.output?.image_url && onDownload) {
      onDownload(taskDetail.output.image_url)
    }
  }

  const renderContent = () => {
    // Loading state
    if (isLoading) {
      return (
        <div className="space-y-4">
          <Skeleton className="w-full h-64 rounded-lg" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      )
    }

    // Error state
    if (error || taskDetail?.status === 'failed') {
      return (
        <div className="text-center py-8">
          <div className="mb-4">
            <svg
              className="w-16 h-16 mx-auto text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Generation Failed
          </h3>
          <p className="text-gray-600 mb-4">
            {error ||
              taskDetail?.error_message ||
              'Something went wrong while generating your image.'}
          </p>
          {onRetry && (
            <Button onClick={onRetry} variant="outline">
              Try Again
            </Button>
          )}
        </div>
      )
    }

    // Processing state
    if (
      taskDetail?.status === 'pending' ||
      taskDetail?.status === 'processing'
    ) {
      return (
        <div className="text-center py-8">
          <div className="mb-4">
            <svg
              className="w-16 h-16 mx-auto text-blue-500 animate-spin"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {taskDetail.status === 'pending' ? 'In Queue' : 'Processing'}
          </h3>
          <p className="text-gray-600">
            {taskDetail.status === 'pending'
              ? 'Your request is in the queue. Please wait...'
              : 'AI is working on your face swap. This may take a few moments...'}
          </p>
          <div className="mt-4">
            <div className="text-sm text-gray-500">
              Task ID: {taskDetail.task_id}
            </div>
          </div>
        </div>
      )
    }

    // Completed state
    if (taskDetail?.status === 'completed' && taskDetail.output?.image_url) {
      return (
        <div className="space-y-4">
          <div className="relative group">
            <img
              src={taskDetail.output.image_url}
              alt="Generated face swap result"
              className="w-full h-auto rounded-lg shadow-lg object-contain max-h-[500px]"
            />
          </div>
          <div className="flex justify-center space-x-4">
            <Button
              onClick={handleDownload}
              className="flex items-center space-x-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <span>Download</span>
            </Button>
          </div>
          <div className="text-center text-sm text-gray-500">
            <div>Task ID: {taskDetail.task_id}</div>
            {taskDetail.completed_at && (
              <div>
                Completed: {new Date(taskDetail.completed_at).toLocaleString()}
              </div>
            )}
          </div>
        </div>
      )
    }

    // No result state
    return (
      <div className="text-center py-12">
        <div className="mb-4">
          <svg
            className="w-16 h-16 mx-auto text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          No Result Yet
        </h3>
        <p className="text-gray-600">
          Upload your images and click generate to create a face swap
        </p>
      </div>
    )
  }

  return (
    <Card className={`p-6 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">Result</h3>
      {renderContent()}
    </Card>
  )
}
