'use client'

import React from 'react'

interface TabSwitcherProps {
  activeTab: 'creations' | 'history'
  onTabChange: (tab: 'creations' | 'history') => void
  generateCount?: number
  historyCount?: number
  className?: string
}

export function TabSwitcher({
  activeTab,
  onTabChange,
  generateCount,
  historyCount = 0,
  className = '',
}: TabSwitcherProps) {
  return (
    <div className={`flex space-x-1 ${className}`}>
      <button
        onClick={() => onTabChange('creations')}
        className={`
          relative flex items-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200
          ${
            activeTab === 'creations'
              ? 'text-gray-900 after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-gradient-to-r after:from-pink-500 after:to-purple-600'
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
          }
        `}
      >
        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z"
            clipRule="evenodd"
          />
        </svg>
        Creations
      </button>

      <button
        onClick={() => onTabChange('history')}
        className={`
          relative flex items-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200
          ${
            activeTab === 'history'
              ? 'text-gray-900 after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-gradient-to-r after:from-pink-500 after:to-purple-600'
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
          }
        `}
      >
        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z"
            clipRule="evenodd"
          />
        </svg>
        History
        {historyCount > 0 && (
          <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-600">
            {historyCount}
          </span>
        )}
      </button>
    </div>
  )
}
