'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Footer,
} from '@ui/components/dialog'
import { Button } from '@ui/components/button'
import { Badge } from '@ui/components/badge'
import { Card, CardContent } from '@ui/components/card'
import { Skeleton } from '@ui/components/skeleton'
import {
  Download,
  RefreshCw,
  Share2,
  ZoomIn,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  ArrowRight,
  Equal,
} from 'lucide-react'
import { downloadImage } from '../lib/utils'
import toast from 'react-hot-toast'
import ReactCompareImage from 'react-compare-image'
import { HistoryItem } from '@/types/history'
import { FieldMapping } from './HistoryTab'

// 默认字段映射（保持 face-swap 向后兼容）
const DEFAULT_FIELD_MAPPING: FieldMapping = {
  firstImage: 'swap_image',
  secondImage: 'target_image',
  resultImage: 'image_url',
  labels: {
    firstImage: 'Swap Image',
    secondImage: 'Target Image',
  },
}

interface TaskDetailModalProps {
  isOpen: boolean
  onClose: () => void
  task: HistoryItem | null
  onRegenerate?: (firstImage: string, secondImage?: string) => void
  fieldMapping?: FieldMapping // 新增字段映射配置
}

interface ImagePreviewProps {
  src: string
  alt: string
  title: string
  onZoomIn: (src: string) => void
  className?: string
}

// 可复用的图片预览组件
const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  alt,
  title,
  onZoomIn,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleImageLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleImageError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <Card className="overflow-hidden">
        <CardContent className="p-0 relative aspect-video">
          {isLoading && <Skeleton className="absolute inset-0 w-full h-full" />}

          {hasError ? (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <div className="text-center">
                <XCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">Failed to load image</p>
              </div>
            </div>
          ) : (
            <>
              <img
                src={src}
                alt={alt}
                className="w-full h-full object-cover"
                onLoad={handleImageLoad}
                onError={handleImageError}
              />

              {!isLoading && (
                <button
                  onClick={() => onZoomIn(src)}
                  className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100"
                >
                  <div className="bg-white/90 rounded-full p-2">
                    <ZoomIn className="h-4 w-4 text-gray-700" />
                  </div>
                </button>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 状态徽章组件
const StatusBadge: React.FC<{ status: HistoryItem['status'] }> = ({
  status,
}) => {
  const statusConfig = {
    SUCCESS: {
      label: 'Completed',
      badgeStatus: 'success' as const,
      icon: CheckCircle,
    },
    FAILED: {
      label: 'Failed',
      badgeStatus: 'error' as const,
      icon: XCircle,
    },
    PROCESSING: {
      label: 'Processing',
      badgeStatus: 'info' as const,
      icon: Clock,
    },
    PENDING: {
      label: 'Pending',
      badgeStatus: 'warning' as const,
      icon: AlertCircle,
    },
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <Badge status={config.badgeStatus} className="text-xs flex">
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </Badge>
  )
}

// 任务信息组件
const TaskInfo: React.FC<{ task: HistoryItem }> = ({ task }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }

  const getDuration = () => {
    if (!task.completed_at) return null
    const start = new Date(task.created_at).getTime()
    const end = new Date(task.completed_at).getTime()
    const duration = Math.round((end - start) / 1000)
    return `${duration}s`
  }

  return (
    <Card>
      <CardContent className="p-4 space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Task Details</h4>
          <StatusBadge status={task.status} />
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Task ID:</span>
            <span className="font-mono text-xs">{task.external_task_id}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">Created:</span>
            <span>{formatDate(task.created_at)}</span>
          </div>

          {task.completed_at && (
            <div className="flex justify-between">
              <span className="text-gray-600">Completed:</span>
              <span>{formatDate(task.completed_at)}</span>
            </div>
          )}

          {getDuration() && (
            <div className="flex justify-between">
              <span className="text-gray-600">Duration:</span>
              <span>{getDuration()}</span>
            </div>
          )}
        </div>

        {task.status === 'FAILED' && task?.status && (
          <div className="mt-3 p-3 bg-red-50 rounded-lg">
            <p className="text-sm text-red-600">Error: {task.status}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export const TaskDetailModal: React.FC<TaskDetailModalProps> = ({
  isOpen,
  onClose,
  task,
  onRegenerate,
  fieldMapping = DEFAULT_FIELD_MAPPING,
}) => {
  const [enlargedImage, setEnlargedImage] = useState<string | null>(null)

  if (!task) return null

  // 获取图片 URL 的辅助函数
  const getImageUrl = (
    imageType: 'first' | 'second' | 'result'
  ): string | null => {
    switch (imageType) {
      case 'first':
        return task.input_params?.[fieldMapping.firstImage] || null
      case 'second':
        return fieldMapping.secondImage
          ? task.input_params?.[fieldMapping.secondImage] || null
          : null
      case 'result':
        return fieldMapping.resultImage
          ? task.result_data?.[fieldMapping.resultImage] || null
          : null
      default:
        return null
    }
  }

  // 获取图片标签的辅助函数
  const getImageLabel = (imageType: 'first' | 'second'): string => {
    switch (imageType) {
      case 'first':
        return fieldMapping.labels?.firstImage || 'First image'
      case 'second':
        return fieldMapping.labels?.secondImage || 'Second image'
      default:
        return ''
    }
  }

  const handleDownload = async (imageUrl: string, index?: number) => {
    try {
      await downloadImage(imageUrl, undefined, index)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const handleShare = async () => {
    const resultUrl = getImageUrl('result')
    if (resultUrl) {
      try {
        await navigator.clipboard.writeText(resultUrl)
        toast.success('Image URL copied to clipboard!')
      } catch (error) {
        toast.error('Failed to copy to clipboard')
      }
    }
  }

  const handleRegenerate = () => {
    if (onRegenerate) {
      const firstImage = getImageUrl('first')
      const secondImage = getImageUrl('second')

      if (firstImage && (!fieldMapping.secondImage || secondImage)) {
        onRegenerate(firstImage, secondImage || undefined)
        onClose()
      }
    }
  }

  return (
    <>
      {/* 主弹窗 */}
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto bg-white">
          <DialogHeader>
            <DialogTitle>Face Swap Details</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* 图片展示区域 */}
            <div className="flex items-center gap-4 overflow-x-auto">
              {/* 第一张图片 */}
              {getImageUrl('first') && (
                <div className="w-48 flex-shrink-0">
                  <ImagePreview
                    src={getImageUrl('first')!}
                    alt={getImageLabel('first')}
                    title={getImageLabel('first')}
                    onZoomIn={setEnlargedImage}
                  />
                </div>
              )}

              {/* 第二张图片（如果存在） */}
              {fieldMapping.secondImage && getImageUrl('second') && (
                <div className="w-48 flex-shrink-0">
                  <ImagePreview
                    src={getImageUrl('second')!}
                    alt={getImageLabel('second')}
                    title={getImageLabel('second')}
                    onZoomIn={setEnlargedImage}
                  />
                </div>
              )}

              {/* 箭头指示 */}
              {getImageUrl('result') && (
                <div className="flex-shrink-0">
                  <ArrowRight className="h-6 w-6 text-gray-400" />
                </div>
              )}

              {/* 结果图片 */}
              {getImageUrl('result') && (
                <div className="w-48 flex-shrink-0">
                  <ImagePreview
                    src={getImageUrl('result')!}
                    alt="Result Image"
                    title="Result Image"
                    onZoomIn={setEnlargedImage}
                  />
                </div>
              )}

              {/* <ReactCompareImage
              leftImage={getImageUrl('first')}
              rightImage={getImageUrl('second')}
              sliderLineWidth={2}
              sliderLineColor="#3B82F6"
              leftImageCss={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                borderRadius: '0.75rem',
              }}
              rightImageCss={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                borderRadius: '0.75rem',
              }}
              handle={
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center shadow-md rotate-90">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    className="w-5 h-5 text-white"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                    />
                  </svg>
                </div>
              }
              hover={false}
              skeleton={
                <div className="w-full h-full bg-gray-100 animate-pulse" />
              }
            /> */}
            </div>
          </div>

          {/* 任务信息 */}
          <TaskInfo task={task} />

          <DialogFooter className="gap-2 flex-wrap">
            {/* Download buttons for each image */}
            {getImageUrl('first') && (
              <Button
                variant="outline"
                onClick={() => handleDownload(getImageUrl('first')!, 0)}
              >
                <Download className="h-4 w-4 mr-2" />
                Download {getImageLabel('first')}
              </Button>
            )}

            {fieldMapping.secondImage && getImageUrl('second') && (
              <Button
                variant="outline"
                onClick={() => handleDownload(getImageUrl('second')!, 1)}
              >
                <Download className="h-4 w-4 mr-2" />
                Download {getImageLabel('second')}
              </Button>
            )}

            {getImageUrl('result') && (
              <>
                <Button
                  variant="outline"
                  onClick={() => handleDownload(getImageUrl('result')!, 2)}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Result
                </Button>

                <Button variant="outline" onClick={handleShare}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </>
            )}

            <Button variant="outline" onClick={handleRegenerate}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Regenerate
            </Button>

            <Button onClick={onClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 图片放大查看弹窗 */}
      <Dialog
        open={!!enlargedImage}
        onOpenChange={() => setEnlargedImage(null)}
      >
        <DialogContent className="max-w-5xl max-h-[95vh] p-2 bg-white">
          <div className="relative">
            <img
              src={enlargedImage || ''}
              alt="Enlarged view"
              className="w-full h-auto max-h-[90vh] object-contain"
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
