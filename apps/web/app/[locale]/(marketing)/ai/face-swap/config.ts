// Face Swap Configuration
export const FACE_SWAP_CONFIG = {
  // Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  },

  // UI Configuration
  ui: {
    // Pagination
    historyPageSize: 10,

    sampleOriginalImages: [
      {
        id: 1,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/3c8e5fb8-c89f-4d6e-8cc3-dd3b497e4f60.webp',
        alt: 'Professional male portrait',
        badge: 'Exclusive',
      },
      {
        id: 2,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/ad3c32bb-d472-489c-9e3a-6631ced09c1d.webp',
        alt: 'Professional female portrait',
        badge: 'Exclusive',
      },
      {
        id: 3,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/79a14590-3060-418b-bd94-bbca6b1bc758.webp',
        alt: 'Casual male portrait',
        badge: 'Exclusive',
      },
      {
        id: 4,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/431d31f6-4aa9-47ba-b4ec-bf0e7a094ec8.webp',
        alt: 'Casual female portrait',
      },
    ],

    // Sample images
    sampleTargetImages: [
      {
        id: 1,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/face4.png',
        alt: 'Professional male portrait',
      },
      {
        id: 2,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/face3.png',
        alt: 'Professional female portrait',
      },
      {
        id: 3,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/face2.png',
        alt: 'Casual male portrait',
      },
      {
        id: 4,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/face1.png',
        alt: 'Casual female portrait',
      },
    ],

    // Animation and transition settings
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },
  },

  // Feature Flags
  features: {
    enableHistory: true,
    enableSampleImages: true,
    enableDownload: true,
    enableWebhook: true,

    // Development features
    enableDevTools: process.env.NODE_ENV === 'development',
    enableDebugLogs: process.env.NODE_ENV === 'development',
  },

  // Error Messages
  messages: {
    errors: {
      uploadFailed: 'Image upload failed, please try again.',
      generateFailed: 'Face swap failed, please try again.',
      loginRequired: 'Please login to use this feature.',
      invalidFileType: 'Please select a valid image file (JPG, PNG, WebP).',
      fileTooLarge: 'File size cannot exceed 10MB.',
      networkError: 'Network error, please check your connection.',
    },
    success: {
      uploadComplete: 'Image uploaded successfully!',
      generateComplete: 'Face swap completed!',
      downloadStarted: 'Download started.',
    },
    info: {
      uploadInProgress: 'Uploading image...',
      generateInProgress: 'Generating face swap...',
      processingTask: 'Processing your request...',
    },
  },
} as const

// Type helpers
export type SampleImage = {
  readonly id: number
  readonly url: string
  readonly alt: string
  readonly badge?: string
}
export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed'

export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = FACE_SWAP_CONFIG.upload.allowedTypes as readonly string[]
  return (
    allowedTypes.includes(file.type) &&
    file.size <= FACE_SWAP_CONFIG.upload.maxFileSize
  )
}

export const getErrorMessage = (
  errorKey: keyof typeof FACE_SWAP_CONFIG.messages.errors
): string => {
  return FACE_SWAP_CONFIG.messages.errors[errorKey]
}

export const getSuccessMessage = (
  successKey: keyof typeof FACE_SWAP_CONFIG.messages.success
): string => {
  return FACE_SWAP_CONFIG.messages.success[successKey]
}

export const getInfoMessage = (
  infoKey: keyof typeof FACE_SWAP_CONFIG.messages.info
): string => {
  return FACE_SWAP_CONFIG.messages.info[infoKey]
}
