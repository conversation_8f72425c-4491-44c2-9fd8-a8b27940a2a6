import { NextRequest, NextResponse } from 'next/server'
import { setThemeInResponse } from '../../../lib/theme-server'
import { type Theme } from '../../../lib/theme-constants'

export async function POST(request: NextRequest) {
  try {
    const { theme, isUserOverride = true } = await request.json()

    // 验证主题值
    if (!theme || (theme !== 'light' && theme !== 'dark')) {
      return NextResponse.json(
        { error: 'Invalid theme. Must be "light" or "dark".' },
        { status: 400 }
      )
    }

    // 创建响应
    const response = NextResponse.json({
      success: true,
      theme,
      message: `Theme set to ${theme}${
        isUserOverride ? ' (user preference)' : ' (auto)'
      }`,
    })

    // 设置主题 cookie
    setThemeInResponse(response, theme as Theme, isUserOverride)

    return response
  } catch (error) {
    console.error('Theme API error:', error)
    return NextResponse.json({ error: 'Failed to set theme' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const theme = request.cookies.get('theme')?.value || 'dark'
    const userOverride = request.cookies.get('user-theme-override')?.value

    return NextResponse.json({
      theme,
      userOverride,
      hasUserOverride: Boolean(userOverride),
    })
  } catch (error) {
    console.error('Theme API error:', error)
    return NextResponse.json({ error: 'Failed to get theme' }, { status: 500 })
  }
}
