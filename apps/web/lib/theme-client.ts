'use client'

import Cookies from 'js-cookie'
import {
  type Theme,
  THEME_COOKIE_NAME,
  USER_THEME_OVERRIDE_COOKIE_NAME,
} from './theme-constants'

/**
 * 客户端获取当前主题
 */
export function getClientTheme(): Theme {
  // 优先从 cookie 获取
  const theme = Cookies.get(THEME_COOKIE_NAME) as Theme | undefined
  if (theme && (theme === 'light' || theme === 'dark')) {
    return theme
  }

  // 默认主题
  return 'dark'
}

/**
 * 客户端设置主题（用户手动切换）
 */
export function setClientTheme(
  theme: Theme,
  isUserOverride: boolean = true
): void {
  // 设置主题 cookie
  Cookies.set(THEME_COOKIE_NAME, theme, {
    expires: 365, // 1 年
    path: '/',
    sameSite: 'lax',
    secure: process.env.NODE_ENV === 'production',
  })

  // 如果是用户手动设置，记录用户偏好
  if (isUserOverride) {
    Cookies.set(USER_THEME_OVERRIDE_COOKIE_NAME, theme, {
      expires: 365, // 1 年
      path: '/',
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
    })
  }

  // 立即应用到 DOM
  applyThemeToDOM(theme)
}

/**
 * 应用主题到 DOM
 */
export function applyThemeToDOM(theme: Theme): void {
  if (typeof window !== 'undefined') {
    document.documentElement.classList.remove('light', 'dark')
    document.documentElement.classList.add(theme)
  }
}

/**
 * 切换主题
 */
export function toggleClientTheme(): Theme {
  const currentTheme = getClientTheme()
  const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light'
  setClientTheme(newTheme, true)
  return newTheme
}

/**
 * 清除用户主题偏好（恢复自动主题）
 */
export function clearUserThemeOverride(): void {
  Cookies.remove(USER_THEME_OVERRIDE_COOKIE_NAME, { path: '/' })

  // 重新加载页面以应用自动主题
  if (typeof window !== 'undefined') {
    window.location.reload()
  }
}

/**
 * 检查用户是否有主题偏好设置
 */
export function hasUserThemeOverride(): boolean {
  const override = Cookies.get(USER_THEME_OVERRIDE_COOKIE_NAME)
  return Boolean(override)
}

/**
 * 获取用户主题偏好
 */
export function getUserThemeOverride(): Theme | null {
  const override = Cookies.get(USER_THEME_OVERRIDE_COOKIE_NAME) as
    | Theme
    | undefined
  if (override && (override === 'light' || override === 'dark')) {
    return override
  }
  return null
}
