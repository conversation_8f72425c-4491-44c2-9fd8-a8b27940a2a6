import { cookies } from 'next/headers'
import type { NextRequest, NextResponse } from 'next/server'
import {
  type Theme,
  THEME_COOKIE_NAME,
  USER_THEME_OVERRIDE_COOKIE_NAME,
  getThemeByPath,
} from './theme-constants'

/**
 * 服务端获取当前主题
 * 优先级：用户手动设置 > 路径自动判断 > 默认主题
 */
export async function getServerTheme(pathname?: string): Promise<Theme> {
  const cookieStore = await cookies()
  // 如果有路径信息，根据路径判断
  if (pathname) {
    return getThemeByPath(pathname)
  }
  // 检查用户是否手动设置了主题偏好
  const userOverride = cookieStore.get(USER_THEME_OVERRIDE_COOKIE_NAME)
    ?.value as Theme | undefined
  if (userOverride && (userOverride === 'light' || userOverride === 'dark')) {
    return userOverride
  }

  // 检查当前主题 cookie
  const currentTheme = cookieStore.get(THEME_COOKIE_NAME)?.value as
    | Theme
    | undefined
  if (currentTheme && (currentTheme === 'light' || currentTheme === 'dark')) {
    return currentTheme
  }

  // 默认主题
  return 'dark'
}

/**
 * 在 middleware 中设置主题 cookie
 */
export function setThemeInResponse(
  response: NextResponse,
  theme: Theme,
  isUserOverride: boolean = false
): NextResponse {
  // 设置主题 cookie
  response.cookies.set(THEME_COOKIE_NAME, theme, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 年
    sameSite: 'lax',
    secure: process.env.NODE_ENV === 'production',
  })

  // 如果是用户手动设置，记录用户偏好
  if (isUserOverride) {
    response.cookies.set(USER_THEME_OVERRIDE_COOKIE_NAME, theme, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 年
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
    })
  }

  return response
}

/**
 * 从请求中获取当前主题
 */
export function getThemeFromRequest(request: NextRequest): Theme | null {
  const theme = request.cookies.get(THEME_COOKIE_NAME)?.value as
    | Theme
    | undefined
  if (theme && (theme === 'light' || theme === 'dark')) {
    return theme
  }
  return null
}

/**
 * 从请求中获取用户主题偏好
 */
export function getUserThemeOverride(request: NextRequest): Theme | null {
  const override = request.cookies.get(USER_THEME_OVERRIDE_COOKIE_NAME)
    ?.value as Theme | undefined
  if (override && (override === 'light' || override === 'dark')) {
    return override
  }
  return null
}

/**
 * 清除用户主题偏好（恢复自动主题）
 */
export function clearUserThemeOverride(response: NextResponse): NextResponse {
  response.cookies.delete(USER_THEME_OVERRIDE_COOKIE_NAME)
  return response
}

/**
 * 检查是否需要更新主题
 */
export function shouldUpdateTheme(
  request: NextRequest,
  pathname: string
): { shouldUpdate: boolean; newTheme: Theme } {
  const userOverride = getUserThemeOverride(request)

  // 如果用户有手动设置，不自动更新
  if (userOverride) {
    console.log(`🎨 shouldUpdateTheme: 用户有手动设置 ${userOverride}`)
    return { shouldUpdate: false, newTheme: userOverride }
  }

  const currentTheme = getThemeFromRequest(request)
  const expectedTheme = getThemeByPath(pathname)

  console.log(
    `🎨 shouldUpdateTheme: 路径=${pathname}, 当前主题=${currentTheme}, 期望主题=${expectedTheme}`
  )

  // 如果当前主题与期望主题不一致，需要更新
  const shouldUpdate = currentTheme !== expectedTheme

  return { shouldUpdate, newTheme: expectedTheme }
}
