import { routing } from '@i18n/routing'
import createMiddleware from 'next-intl/middleware'
import type { NextRequest, NextResponse } from 'next/server'
import { shouldUpdateTheme, setThemeInResponse } from './lib/theme-server'

const intlMiddleware = createMiddleware(routing)

export default async function middleware(req: NextRequest) {
  // 首先处理国际化
  const response = intlMiddleware(req) as NextResponse

  // 获取路径名（移除语言前缀）
  const pathname = req.nextUrl.pathname

  // 检查是否匹配已知的语言代码
  const supportedLocales = [
    'en',
    'es',
    'de',
    'fr',
    'ja',
    'ko',
    'pt',
    'ru',
    'th',
    'vi',
    'zh-cn',
    'zh-hk',
    'zh-tw',
  ]
  const localeMatch = pathname.match(/^\/([a-z]{2}(-[a-z]{2})?)\//i)
  const matchedLocale = localeMatch ? localeMatch[1].toLowerCase() : null

  const pathWithoutLocale =
    localeMatch && supportedLocales.includes(matchedLocale!)
      ? pathname.replace(localeMatch[0], '/')
      : pathname

  console.log(
    `🎨 Middleware: 原始路径 ${pathname}, 处理后路径 ${pathWithoutLocale}`
  )

  // 检查是否需要更新主题
  const { shouldUpdate, newTheme } = shouldUpdateTheme(req, pathWithoutLocale)

  console.log(
    `🎨 Middleware: 主题检查结果 - shouldUpdate: ${shouldUpdate}, newTheme: ${newTheme}, 路径: ${pathWithoutLocale}`
  )

  if (shouldUpdate) {
    console.log(
      `🎨 Middleware: 路径 ${pathWithoutLocale} 自动切换主题到 ${newTheme}`
    )
    setThemeInResponse(response, newTheme, false)
  }

  return response
}
//

export const config = {
  matcher: ['/((?!api|_next|.*\\..*).*)'],
}
