import { cookies } from 'next/headers'
import { THEME_COOKIE_NAME } from '../lib/theme-constants'
import type { Theme } from '../lib/theme-constants'

interface ServerThemeProviderProps {
  children: React.ReactNode
}

export async function ServerThemeProvider({
  children,
}: ServerThemeProviderProps) {
  // 从服务端 cookie 获取主题
  const cookieStore = await cookies()
  const theme = (cookieStore.get(THEME_COOKIE_NAME)?.value as Theme) || 'dark'

  return (
    <>
      {/* 在 HTML head 中设置初始主题，避免闪烁 */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            (function() {
              try {
                const theme = '${theme}';
                document.documentElement.classList.remove('light', 'dark');
                document.documentElement.classList.add(theme);
                // 设置一个标记表示服务端主题已应用
                window.__INITIAL_THEME__ = theme;
              } catch (e) {
                console.error('Failed to set initial theme:', e);
              }
            })();
          `,
        }}
      />
      {children}
    </>
  )
}
